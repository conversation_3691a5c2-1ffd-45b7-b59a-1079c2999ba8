/* ===========  
 * 作者: <PERSON><PERSON><PERSON><PERSON>  
 * 创建时间: 2025/05/29 
 * 非作者最新修改时间：  
 * ===========  
 */
using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Unity.Passport.Runtime;
using Unity.Passport.Runtime.Model;
using Unity.UOS.Auth;
using Unity.UOS.CloudSave;
using Unity.UOS.CloudSave.Model.Files;
using UnityEngine;
using UnityEngine.Networking;
using VIC.Networking.UOS;

/// <summary>
/// UOS云端存储管理类，提供与Unity Online Services存档系统交互的功能
/// </summary>
public static class CloudSaveManagement_UOS
{
    #region 私有字段

    // 缓存的头像列表
    private static readonly List<PFPItem> cachedProfilePictures = new();
    private static readonly Dictionary<string, Texture2D> cachedAvatarTextures = new();
    private static PFPItem currentUserProfilePicture = null;
    private static bool isProfilePicturesLoaded = false;
    private static bool isInitialized = false;

    // 防止重复获取用户头像的锁和标志
    private static bool _isRefreshingUserProfile = false;
    private static readonly object _userProfileLock = new object();
    private static UniTaskCompletionSource<PFPItem> _refreshCompletionSource = null;

    #endregion

    #region 初始化

    /// <summary>
    /// 初始化云端存储
    /// </summary>
    /// <returns>初始化操作的任务</returns>
    public static async UniTask Initialize()
    {
        if (isInitialized)
        {
            Debug.Log("CloudSaveManagement_UOS 已经初始化");
            return;
        }

        await InitializeCloudSave();

        // 检查是否已登录，只有登录后才加载默认头像
        if (PassportLoginSDK.Identity.CheckIsLoggedIn())
        {
            await LoadDefaultProfilePicturesOnInit();
        }
        else
        {
            Debug.Log("用户未登录，跳过默认头像加载");
        }

        isInitialized = true;
        Debug.Log("CloudSaveManagement_UOS 初始化完成");
    }

    /// <summary>
    /// 确保头像预加载完成（用于登录后调用）
    /// </summary>
    /// <returns>预加载操作的任务</returns>
    public static async UniTask EnsureAvatarsPreloaded()
    {
        try
        {
            if (!isInitialized)
            {
                await Initialize();
                return; // Initialize已经处理了头像预加载，直接返回
            }

            // 只有在真正需要时才重新加载
            if (!isProfilePicturesLoaded || cachedProfilePictures.Count == 0)
            {
                Debug.Log("头像未预加载，开始预加载...");
                await LoadDefaultProfilePicturesOnInit();
            }
            else
            {
                Debug.Log($"头像已预加载完成，共 {cachedProfilePictures.Count} 个");
            }
        }
        catch (Exception ex) when (ex.Message.Contains("未找到User") || ex.Message.Contains("Unauthenticated"))
        {
            Debug.LogWarning($"头像预加载时用户认证异常，跳过预加载: {ex.Message}");
            isProfilePicturesLoaded = false;
        }
        catch (Exception ex)
        {
            Debug.LogWarning($"头像预加载失败，但不影响系统运行: {ex.Message}");
            isProfilePicturesLoaded = false;
        }
    }

    /// <summary>
    /// 初始化云端存储的内部实现
    /// </summary>
    private static async UniTask InitializeCloudSave()
    {
        try
        {
            await CloudSaveSDK.InitializeAsync();
            Debug.Log("云端存储SDK初始化成功");
        }
        catch (Exception ex)
        {
            Debug.LogError($"云端存储SDK初始化失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 初始化时加载默认头像列表并预加载纹理
    /// </summary>
    private static async UniTask LoadDefaultProfilePicturesOnInit()
    {
        try
        {
            Debug.Log("开始加载默认头像列表...");

            // 如果开发者未上传默认头像，将返回空列表
            List<PFPItem> profilePictures = await CloudSaveSDK.Instance.Files.ListDefaultProfilePicturesAsync();

            // 更新缓存
            cachedProfilePictures.Clear();
            cachedProfilePictures.AddRange(profilePictures);
            cachedAvatarTextures.Clear();

            Debug.Log($"成功获取默认头像列表，共 {profilePictures.Count} 个");

            // 预加载所有默认头像纹理
            if (profilePictures.Count > 0)
            {
                await PreloadDefaultAvatarTextures(profilePictures);
            }

            isProfilePicturesLoaded = true;
        }
        catch (Exception e) when (UOSExceptionHandler.IsAuthenticationException(e))
        {
            UOSExceptionHandler.HandleAuthenticationException(e, "默认头像加载", false);
            // 尝试处理Token过期
            HandleTokenExpiredInCloudSaveAsync();
            // 用户重新登录后可以手动刷新头像列表
            isProfilePicturesLoaded = false;
        }
        catch (Exception e) when (UOSExceptionHandler.IsCloudSaveClientException(e) || UOSExceptionHandler.IsCloudSaveServerException(e))
        {
            UOSExceptionHandler.HandleCloudSaveException(e, "默认头像加载", false);
            // 客户端/服务器异常也不抛出，允许系统继续运行
            isProfilePicturesLoaded = false;
        }
        catch (Exception e)
        {
            UOSExceptionHandler.HandleGeneralException(e, "默认头像加载", false);
            // 其他异常也不抛出，允许系统继续运行
            isProfilePicturesLoaded = false;
        }
    }

    /// <summary>
    /// 预加载默认头像纹理到缓存
    /// </summary>
    /// <param name="profilePictures">头像列表</param>
    private static async UniTask PreloadDefaultAvatarTextures(List<PFPItem> profilePictures)
    {
        Debug.Log($"开始预加载 {profilePictures.Count} 个默认头像纹理...");

        int successCount = 0;
        int failCount = 0;

        foreach (var pfpItem in profilePictures)
        {
            if (!string.IsNullOrEmpty(pfpItem.DownloadUrl))
            {
                try
                {
                    var texture = await LoadWebTexture(pfpItem.DownloadUrl);
                    if (texture != null)
                    {
                        cachedAvatarTextures[pfpItem.PfpId] = texture;
                        successCount++;
                        // Debug.Log($"预加载头像成功: {pfpItem.PfpId}");
                    }
                    else
                    {
                        failCount++;
                        Debug.LogWarning($"预加载头像失败: {pfpItem.PfpId} - 纹理为空");
                    }
                }
                catch (Exception ex)
                {
                    failCount++;
                    Debug.LogError($"预加载头像异常: {pfpItem.PfpId} - {ex.Message}");
                }
            }
            else
            {
                failCount++;
                Debug.LogWarning($"头像下载链接为空: {pfpItem.PfpId}");
            }
        }

        Debug.Log($"默认头像预加载完成: 成功 {successCount} 个，失败 {failCount} 个");
    }

    #endregion

    #region 头像上传功能

    /// <summary>
    /// 上传头像到云端存储
    /// </summary>
    /// <param name="texture">要上传的头像纹理</param>
    /// <param name="format">头像格式（可选，默认为PNG）</param>
    /// <returns>上传结果，包含头像ID和下载链接</returns>
    public static async UniTask<SavePFPResponse> UploadAvatarAsync(Texture2D texture, ProfilePictureFormat format = ProfilePictureFormat.Png)
    {
        if (texture == null)
        {
            Debug.LogError("上传头像失败: 纹理为空");
            return null;
        }

        try
        {
            // 将纹理转换为字节数组
            byte[] bytes = GetTextureBytes(texture, format);
            if (bytes == null || bytes.Length == 0)
            {
                Debug.LogError("上传头像失败: 无法转换纹理为字节数组");
                return null;
            }

            Debug.Log($"开始上传头像，格式: {format}, 大小: {bytes.Length} 字节");

            // 通过字节数组上传头像
            var response = await CloudSaveSDK.Instance.Files.SaveProfilePictureAsync(bytes, format);

            Debug.Log($"头像上传成功! 头像ID: {response.PfpId}, 下载链接: {response.DownloadUrl}");
            return response;
        }
        catch (Exception e) when (e.GetType().Name.Contains("CloudSaveClientException"))
        {
            Debug.LogErrorFormat("上传头像失败，客户端异常，格式 {0}, 异常: {1}", format, e);
            return null;
        }
        catch (Exception e) when (e.GetType().Name.Contains("CloudSaveServerException"))
        {
            Debug.LogErrorFormat("上传头像失败，服务器异常，格式 {0}, 异常: {1}", format, e);
            return null;
        }
        catch (Exception e)
        {
            Debug.LogErrorFormat("上传头像失败，未知异常，格式 {0}, 异常: {1}", format, e);
            return null;
        }
    }

    /// <summary>
    /// 根据格式将纹理转换为字节数组
    /// </summary>
    /// <param name="texture">要转换的纹理</param>
    /// <param name="format">目标格式</param>
    /// <returns>字节数组</returns>
    private static byte[] GetTextureBytes(Texture2D texture, ProfilePictureFormat format)
    {
        if (texture == null) return null;

        switch (format)
        {
            case ProfilePictureFormat.Png:
                return texture.EncodeToPNG();
            case ProfilePictureFormat.Jpg:
            case ProfilePictureFormat.Jpeg:
                return texture.EncodeToJPG();
            default:
                Debug.LogWarning($"不支持的格式 {format}，使用PNG格式");
                return texture.EncodeToPNG();
        }
    }

    /// <summary>
    /// 根据文件扩展名确定头像格式
    /// </summary>
    /// <param name="fileName">文件名</param>
    /// <returns>头像格式</returns>
    public static ProfilePictureFormat GetFormatFromFileName(string fileName)
    {
        if (string.IsNullOrEmpty(fileName)) return ProfilePictureFormat.Png;

        string extension = System.IO.Path.GetExtension(fileName).ToLower();
        return extension switch
        {
            ".jpg" => ProfilePictureFormat.Jpg,
            ".jpeg" => ProfilePictureFormat.Jpeg,
            ".png" => ProfilePictureFormat.Png,
            ".gif" => ProfilePictureFormat.Gif,
            ".svg" => ProfilePictureFormat.Svg,
            _ => ProfilePictureFormat.Png
        };
    }

    #endregion

    #region 头像获取功能

    /// <summary>
    /// 获取默认头像列表
    /// </summary>
    /// <param name="forceRefresh">是否强制刷新缓存</param>
    /// <returns>默认头像列表</returns>
    public static async UniTask<List<PFPItem>> GetDefaultProfilePicturesAsync(bool forceRefresh = false)
    {
        if (!forceRefresh && isProfilePicturesLoaded && cachedProfilePictures.Count > 0)
        {
            Debug.Log($"返回缓存的默认头像列表，共 {cachedProfilePictures.Count} 个");
            return new List<PFPItem>(cachedProfilePictures);
        }

        try
        {
            Debug.Log("开始重新获取默认头像列表...");

            // 如果开发者未上传默认头像，将返回空列表
            List<PFPItem> profilePictures = await CloudSaveSDK.Instance.Files.ListDefaultProfilePicturesAsync();

            // 更新缓存
            cachedProfilePictures.Clear();
            cachedProfilePictures.AddRange(profilePictures);

            // 如果强制刷新，也重新加载纹理
            if (forceRefresh)
            {
                cachedAvatarTextures.Clear();
                if (profilePictures.Count > 0)
                {
                    await PreloadDefaultAvatarTextures(profilePictures);
                }
            }

            isProfilePicturesLoaded = true;

            Debug.Log($"成功获取默认头像列表，共 {profilePictures.Count} 个");
            return new List<PFPItem>(profilePictures);
        }
        catch (Exception e) when (e.Message.Contains("需要重新登录") || e.Message.Contains("AccessToken") || e.Message.Contains("RefreshToken") ||
                                   e.Message.Contains("NeedLogin") || e.Message.Contains("Unauthenticated"))
        {
            Debug.LogWarning($"认证令牌过期，跳过默认头像刷新: {e.Message}");
            // 尝试处理Token过期
            HandleTokenExpiredInCloudSaveAsync();
            return new List<PFPItem>();
        }
        catch (Exception e) when (e.GetType().Name.Contains("CloudSaveClientException"))
        {
            Debug.LogErrorFormat("获取默认头像列表失败，客户端异常: {0}", e);
            return new List<PFPItem>();
        }
        catch (Exception e) when (e.GetType().Name.Contains("CloudSaveServerException"))
        {
            Debug.LogErrorFormat("获取默认头像列表失败，服务器异常: {0}", e);
            return new List<PFPItem>();
        }
        catch (Exception e)
        {
            Debug.LogErrorFormat("获取默认头像列表失败，未知异常: {0}", e);
            return new List<PFPItem>();
        }
    }

    /// <summary>
    /// 获取当前用户的头像信息（带并发控制）
    /// </summary>
    /// <param name="forceRefresh">是否强制刷新缓存</param>
    /// <returns>用户头像信息，如果没有则返回null</returns>
    public static async UniTask<PFPItem> GetCurrentUserProfilePictureAsync(bool forceRefresh = false)
    {
        // 如果不强制刷新且有缓存，直接返回
        if (!forceRefresh && currentUserProfilePicture != null)
        {
            Debug.Log("返回缓存的用户头像信息");
            return currentUserProfilePicture;
        }

        // 使用锁防止多个并发获取
        UniTaskCompletionSource<PFPItem> completionSource = null;
        lock (_userProfileLock)
        {
            // 双重检查，防止在等待锁的过程中其他线程已经获取了
            if (!forceRefresh && currentUserProfilePicture != null)
            {
                Debug.Log("返回缓存的用户头像信息（双重检查）");
                return currentUserProfilePicture;
            }

            if (!_isRefreshingUserProfile)
            {
                _isRefreshingUserProfile = true;
                _refreshCompletionSource = new UniTaskCompletionSource<PFPItem>();
                completionSource = _refreshCompletionSource;
                // 在锁外执行异步获取
                _ = RefreshUserProfilePictureAsync();
            }
            else
            {
                // 如果已经在刷新，等待现有的刷新完成
                completionSource = _refreshCompletionSource;
            }
        }

        // 等待获取完成，使用CompletionSource避免轮询
        if (completionSource != null)
        {
            try
            {
                // 设置超时时间为30秒，使用CancellationToken实现超时
                var cts = new CancellationTokenSource();
                cts.CancelAfterSlim(TimeSpan.FromSeconds(30));

                try
                {
                    return await completionSource.Task.AttachExternalCancellation(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    Debug.LogError("获取用户头像信息超时");
                    return currentUserProfilePicture;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"等待用户头像信息获取时发生异常: {ex.Message}");
                return currentUserProfilePicture;
            }
        }

        return currentUserProfilePicture;
    }

    /// <summary>
    /// 刷新用户头像信息
    /// </summary>
    private static async UniTask RefreshUserProfilePictureAsync()
    {
        try
        {
            Debug.Log("开始获取当前用户头像信息...");

            // 用户可通过获取到的头像信息 (PFPItem) 中的下载链接 (DownloadUrl) 下载头像
            // 如果用户没有头像信息，返回结果中将不包含头像id (PFPId) 和下载链接 (DownloadUrl)
            PFPItem profilePicture = await CloudSaveSDK.Instance.Files.GetProfilePictureAsync();

            // 更新缓存
            currentUserProfilePicture = profilePicture;

            if (profilePicture != null && !string.IsNullOrEmpty(profilePicture.PfpId))
            {
                Debug.Log($"成功获取用户头像信息，头像ID: {profilePicture.PfpId}");
            }
            else
            {
                Debug.Log("用户暂无自定义头像");
            }
        }
        catch (Exception e) when (e.Message.Contains("未找到User") || e.Message.Contains("Unauthenticated"))
        {
            Debug.LogWarning($"获取用户头像信息时认证异常: {e.Message}");
            // 认证异常时尝试重新登录
            HandleTokenExpiredInCloudSaveAsync();
            // 清除缓存，等待重新认证后再获取
            currentUserProfilePicture = null;
        }
        catch (Exception e) when (e.GetType().Name.Contains("CloudSaveClientException"))
        {
            Debug.LogErrorFormat("获取用户头像信息失败，客户端异常: {0}", e);
            // 保持原有缓存
        }
        catch (Exception e) when (e.GetType().Name.Contains("CloudSaveServerException"))
        {
            Debug.LogErrorFormat("获取用户头像信息失败，服务器异常: {0}", e);
            // 保持原有缓存
        }
        catch (Exception e)
        {
            Debug.LogErrorFormat("获取用户头像信息失败，未知异常: {0}", e);
            // 保持原有缓存
        }
        finally
        {
            // 无论成功还是失败，都要重置刷新标志并完成CompletionSource
            lock (_userProfileLock)
            {
                _isRefreshingUserProfile = false;
                if (_refreshCompletionSource != null)
                {
                    try
                    {
                        _refreshCompletionSource.TrySetResult(currentUserProfilePicture);
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"设置CompletionSource结果时发生异常: {ex.Message}");
                    }
                    _refreshCompletionSource = null;
                }
            }
        }
    }

    /// <summary>
    /// 获取所有可用的头像列表（包括默认头像和用户头像）
    /// </summary>
    /// <param name="forceRefresh">是否强制刷新缓存</param>
    /// <returns>所有可用头像列表</returns>
    public static async UniTask<List<PFPItem>> GetAllAvailableProfilePicturesAsync(bool forceRefresh = false)
    {
        var allPictures = new List<PFPItem>();

        // 获取默认头像列表
        var defaultPictures = await GetDefaultProfilePicturesAsync(forceRefresh);
        allPictures.AddRange(defaultPictures);

        // 获取用户自定义头像
        // var userPicture = await GetCurrentUserProfilePictureAsync(forceRefresh);
        // if (userPicture != null && !string.IsNullOrEmpty(userPicture.PfpId))
        // {
        //     allPictures.Add(userPicture);
        // }

        Debug.Log($"获取到所有可用头像，共 {allPictures.Count} 个");
        return allPictures;
    }

    /// <summary>
    /// 清除头像缓存
    /// </summary>
    public static void ClearProfilePictureCache()
    {
        cachedProfilePictures.Clear();
        cachedAvatarTextures.Clear();
        currentUserProfilePicture = null;
        isProfilePicturesLoaded = false;
        // Debug.Log("头像缓存已清除");
    }

    /// <summary>
    /// 重新加载默认头像列表（用于用户重新登录后）
    /// </summary>
    /// <returns>是否成功加载</returns>
    public static async UniTask<bool> RefreshDefaultProfilePicturesAsync()
    {
        try
        {
            // Debug.Log("重新加载默认头像列表...");
            await LoadDefaultProfilePicturesOnInit();
            return isProfilePicturesLoaded;
        }
        catch (Exception e)
        {
            Debug.LogError($"重新加载默认头像列表失败: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// 获取缓存的头像列表（不进行网络请求）
    /// </summary>
    /// <returns>缓存的头像列表</returns>
    public static List<PFPItem> GetCachedProfilePictures()
    {
        return new List<PFPItem>(cachedProfilePictures);
    }

    /// <summary>
    /// 根据头像ID获取缓存的头像纹理
    /// </summary>
    /// <param name="pfpId">头像ID</param>
    /// <returns>缓存的头像纹理，如果不存在则返回null</returns>
    public static Texture2D GetCachedAvatarTexture(string pfpId)
    {
        if (string.IsNullOrEmpty(pfpId))
        {
            Debug.LogWarning("头像ID为空");
            return null;
        }

        if (cachedAvatarTextures.TryGetValue(pfpId, out Texture2D texture))
        {
            Debug.Log($"从缓存获取头像纹理: {pfpId}");
            return texture;
        }

        Debug.LogWarning($"缓存中未找到头像纹理: {pfpId}");
        return null;
    }

    /// <summary>
    /// 根据头像ID获取缓存的头像Sprite
    /// </summary>
    /// <param name="pfpId">头像ID</param>
    /// <returns>缓存的头像Sprite，如果不存在则返回null</returns>
    public static Sprite GetCachedAvatarSprite(string pfpId)
    {
        var texture = GetCachedAvatarTexture(pfpId);
        return texture != null ? CreateSpriteFromTexture(texture) : null;
    }

    /// <summary>
    /// 检查头像是否已缓存
    /// </summary>
    /// <param name="pfpId">头像ID</param>
    /// <returns>是否已缓存</returns>
    public static bool IsAvatarCached(string pfpId)
    {
        return !string.IsNullOrEmpty(pfpId) && cachedAvatarTextures.ContainsKey(pfpId);
    }

    /// <summary>
    /// 获取所有缓存的头像ID
    /// </summary>
    /// <returns>缓存的头像ID列表</returns>
    public static List<string> GetCachedAvatarIds()
    {
        return new List<string>(cachedAvatarTextures.Keys);
    }

    #endregion

    #region 角色头像获取功能

    /// <summary>
    /// 通过CloudSave API获取指定角色的头像URL
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="personaId">角色ID</param>
    /// <returns>角色头像URL，如果没有则返回null</returns>
    public static async UniTask<string> GetPersonaAvatarUrlAsync(string userId, string personaId)
    {
        if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(personaId))
        {
            Debug.LogWarning("用户ID或角色ID为空，无法获取角色头像");
            return null;
        }

        try
        {
            Debug.Log($"开始获取角色头像，用户ID: {userId}, 角色ID: {personaId}");

            // 创建Player对象用于查询角色头像
            var players = new List<Player>
            {
                new Player
                {
                    UserId = userId,
                    PersonaId = personaId
                }
            };

            Debug.Log($"调用 ListProfilePicturesAsync，Player数量: {players.Count}");

            // 通过CloudSave API获取指定角色的头像列表
            List<PFPItem> profilePictures =
                await CloudSaveSDK.Instance.Files.ListProfilePicturesAsync(players);

            Debug.Log($"API调用完成，返回头像数量: {profilePictures?.Count ?? 0}");

            if (profilePictures != null && profilePictures.Count > 0)
            {
                // 使用第一个头像（通常角色只有一个头像）
                var avatarItem = profilePictures[0];
                Debug.Log($"第一个头像信息 - ID: {avatarItem.PfpId}, URL: {avatarItem.DownloadUrl}");

                if (!string.IsNullOrEmpty(avatarItem.DownloadUrl))
                {
                    Debug.Log($"成功获取角色头像URL: {avatarItem.DownloadUrl}");
                    return avatarItem.DownloadUrl;
                }
                else
                {
                    Debug.LogWarning("角色头像下载链接为空");
                    return null;
                }
            }
            else
            {
                Debug.Log("角色没有设置头像，API返回空列表");
                return null;
            }
        }
        catch (Exception e) when (e.Message.Contains("未找到User") || e.Message.Contains("Unauthenticated"))
        {
            Debug.LogWarning($"获取角色头像时认证异常: {e.Message}");
            // 认证异常时尝试重新登录
            HandleTokenExpiredInCloudSaveAsync();
            return null;
        }
        catch (Unity.UOS.CloudSave.Exception.CloudSaveClientException e)
        {
            Debug.LogErrorFormat("获取角色头像失败，客户端异常: {0}", e);
            return null;
        }
        catch (Unity.UOS.CloudSave.Exception.CloudSaveServerException e)
        {
            Debug.LogErrorFormat("获取角色头像失败，服务器异常: {0}", e);
            return null;
        }
        catch (Exception e)
        {
            Debug.LogErrorFormat("获取角色头像失败，未知异常: {0}", e);
            return null;
        }
    }

    #endregion

    #region 设置用户头像功能

    /// <summary>
    /// 通过默认头像ID设置用户头像
    /// </summary>
    /// <param name="pfpId">默认头像ID</param>
    /// <param name="personaId">角色ID（可选）</param>
    /// <returns>设置是否成功</returns>
    public static async UniTask<bool> SetUserProfilePictureByIdAsync(string pfpId, string personaId = null)
    {
        if (string.IsNullOrEmpty(pfpId))
        {
            Debug.LogError("设置用户头像失败: 头像ID为空");
            return false;
        }

        try
        {
            Debug.Log($"开始设置用户头像，头像ID: {pfpId}");

            // 创建选项对象
            var options = new SavePFPOptions();
            if (!string.IsNullOrEmpty(personaId))
            {
                options.PersonaId = personaId;
            }

            // 选择默认头像作为用户头像 (仅在开发者配置成功默认头像后生效)
            await CloudSaveSDK.Instance.Files.SaveProfilePictureByIdAsync(pfpId, options);

            // 清除缓存，强制刷新用户头像信息
            currentUserProfilePicture = null;

            Debug.Log($"成功设置用户头像，头像ID: {pfpId}");
            return true;
        }
        catch (Exception e) when (e.GetType().Name.Contains("CloudSaveClientException"))
        {
            Debug.LogErrorFormat("设置用户头像失败，客户端异常，头像ID {0}, 异常: {1}", pfpId, e);
            return false;
        }
        catch (Exception e) when (e.GetType().Name.Contains("CloudSaveServerException"))
        {
            Debug.LogErrorFormat("设置用户头像失败，服务器异常，头像ID {0}, 异常: {1}", pfpId, e);
            return false;
        }
        catch (Exception e)
        {
            Debug.LogErrorFormat("设置用户头像失败，未知异常，头像ID {0}, 异常: {1}", pfpId, e);
            return false;
        }
    }

    #endregion

    #region 头像纹理下载功能

    /// <summary>
    /// 异步下载头像纹理（优先使用缓存）
    /// </summary>
    /// <param name="url">头像URL</param>
    /// <returns>下载的纹理，失败时返回null</returns>
    public static async UniTask<Texture2D> DownloadAvatarTextureAsync(string url)
    {
        if (string.IsNullOrEmpty(url))
        {
            Debug.LogWarning("头像URL为空");
            return null;
        }

        // 首先检查是否是缓存的默认头像URL
        var cachedTexture = TryGetCachedTextureByUrl(url);
        if (cachedTexture != null)
        {
            Debug.Log($"使用缓存的头像纹理: {url}");
            return cachedTexture;
        }

        try
        {
            if (url.StartsWith("http://") || url.StartsWith("https://"))
            {
                return await LoadWebTexture(url);
            }
            else
            {
                Debug.LogWarning($"不支持的头像URL格式: {url}");
                return null;
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"下载头像纹理失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 根据URL尝试获取缓存的纹理
    /// </summary>
    /// <param name="url">头像URL</param>
    /// <returns>缓存的纹理，如果不存在则返回null</returns>
    private static Texture2D TryGetCachedTextureByUrl(string url)
    {
        // 遍历缓存的头像列表，查找匹配的URL
        foreach (var pfpItem in cachedProfilePictures)
        {
            if (pfpItem.DownloadUrl == url && cachedAvatarTextures.TryGetValue(pfpItem.PfpId, out Texture2D texture))
            {
                return texture;
            }
        }
        return null;
    }

    /// <summary>
    /// 下载头像并转换为Sprite
    /// </summary>
    /// <param name="url">头像URL</param>
    /// <returns>下载的Sprite，失败时返回null</returns>
    public static async UniTask<Sprite> DownloadAvatarSpriteAsync(string url)
    {
        var texture = await DownloadAvatarTextureAsync(url);
        return texture != null ? CreateSpriteFromTexture(texture) : null;
    }

    /// <summary>
    /// 协程方式下载头像Sprite（兼容旧代码）
    /// </summary>
    /// <param name="url">头像URL</param>
    /// <param name="callback">下载完成回调</param>
    /// <returns>协程</returns>
    public static IEnumerator DownloadAvatarSpriteCoroutine(string url, Action<Sprite> callback)
    {
        if (string.IsNullOrEmpty(url))
        {
            Debug.LogWarning("头像URL为空");
            callback?.Invoke(null);
            yield break;
        }

        Sprite result = null;

        // 首先检查是否是缓存的默认头像URL
        var cachedTexture = TryGetCachedTextureByUrl(url);
        if (cachedTexture != null)
        {
            Debug.Log($"使用缓存的头像纹理: {url}");
            result = CreateSpriteFromTexture(cachedTexture);
            callback?.Invoke(result);
            yield break;
        }

        // 如果不是缓存的纹理，使用UnityWebRequest下载
        if (url.StartsWith("http://") || url.StartsWith("https://"))
        {
            // 强制使用HTTPS
            string httpsUrl = url.StartsWith("http://") ? url.Replace("http://", "https://") : url;

            using UnityWebRequest request = UnityWebRequestTexture.GetTexture(httpsUrl);
            yield return request.SendWebRequest();

            if (request.result == UnityWebRequest.Result.Success)
            {
                var texture = DownloadHandlerTexture.GetContent(request);
                if (texture != null)
                {
                    result = CreateSpriteFromTexture(texture);
                    Debug.Log($"成功下载头像: {url}");
                }
                else
                {
                    Debug.LogError($"下载头像失败，纹理为空: {url}");
                }
            }
            else
            {
                Debug.LogError($"下载头像失败: {request.error}");
            }
        }
        else
        {
            Debug.LogWarning($"不支持的头像URL格式: {url}");
        }

        callback?.Invoke(result);
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 加载网络图像资源
    /// </summary>
    /// <param name="url">网络图像URL</param>
    /// <returns>下载的纹理对象</returns>
    private static async UniTask<Texture2D> LoadWebTexture(string url)
    {
        try
        {
            // 强制使用HTTPS
            string httpsUrl = url.StartsWith("http://") ? url.Replace("http://", "https://") : url;

            using UnityWebRequest request = UnityWebRequestTexture.GetTexture(httpsUrl);
            await request.SendWebRequest();

            return request.result == UnityWebRequest.Result.Success
                ? DownloadHandlerTexture.GetContent(request)
                : null;
        }
        catch (Exception ex)
        {
            Debug.LogError($"下载网络头像失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 将Texture2D转换为Sprite
    /// </summary>
    /// <param name="texture">目标纹理</param>
    /// <returns>创建的Sprite对象</returns>
    private static Sprite CreateSpriteFromTexture(Texture2D texture)
    {
        return texture == null
            ? null
            : Sprite.Create(texture, new Rect(0, 0, texture.width, texture.height), Vector2.one * 0.5f);
    }

    /// <summary>
    /// 处理Token过期异常（CloudSave模块）
    /// </summary>
    private static void HandleTokenExpiredInCloudSaveAsync()
    {
        Debug.LogWarning("[CloudSaveManagement_UOS] Token已过期，请重新登录（点击开始游戏）");
        // 不再自动重新登录，由用户主动触发完整登录
    }

    #endregion
}