using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Pipes;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using Newtonsoft.Json;
using VIC.Core.Assistant;

namespace VIC.Networking.ChaoXing
{
    /// <summary>
    /// 超星自定义协议处理器 - 专注于协议解析和IPC通信
    /// </summary>
    public static class CustomProtocolHandler
    {
        #region IPC相关字段
        private static CancellationTokenSource _cancellationTokenSource;
        private static Task _ipcListenerTask;
        private static bool _isIPCInitialized = false;
        private static bool _isCommandLineChecked = false;
        private static readonly object _lockObject = new object();
        #endregion

        #region 存储的登录信息
        /// <summary>
        /// 存储的用户ID
        /// </summary>
        public static string StoredUserId { get; private set; } = string.Empty;

        /// <summary>
        /// 存储的角色ID
        /// </summary>
        public static string StoredPersonaId { get; private set; } = string.Empty;
        #endregion

        #region 初始化方法
        /// <summary>
        /// 检查命令行参数（仅执行一次）
        /// </summary>
        public static void CheckCommandLineArgs()
        {
            if (_isCommandLineChecked) return;
            
            _isCommandLineChecked = true;
            Debug.Log("[CustomProtocolHandler] 检查命令行参数");
            
            string[] args = System.Environment.GetCommandLineArgs();
            foreach (string arg in args)
            {
                if (arg.StartsWith("vic://", StringComparison.OrdinalIgnoreCase))
                {
                    Debug.Log($"[CustomProtocolHandler] 发现协议URL: {arg}");
                    HandleProtocolUrl(arg);
                    return;
                }
            }
        }

        /// <summary>
        /// 启动IPC监听
        /// </summary>
        public static void StartIPCListening()
        {
            lock (_lockObject)
            {
                if (_isIPCInitialized)
                {
                    Debug.Log("[CustomProtocolHandler] IPC监听已启动");
                    return;
                }

                try
                {
                    _cancellationTokenSource = new CancellationTokenSource();
                    _ipcListenerTask = Task.Run(async () => await StartIPCListener(_cancellationTokenSource.Token));
                    _isIPCInitialized = true;
                    Debug.Log("[CustomProtocolHandler] IPC监听已启动，管道名称: VICChaoXingPipe");
                    
                    // 注册Unity退出事件
#if UNITY_EDITOR
                    UnityEditor.EditorApplication.playModeStateChanged += OnPlayModeStateChanged;
#endif
                    Application.quitting += OnApplicationQuitting;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[CustomProtocolHandler] IPC启动失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 停止IPC监听
        /// </summary>
        public static void StopIPCListening()
        {
            lock (_lockObject)
            {
                if (!_isIPCInitialized)
                {
                    return;
                }

                try
                {
                    Debug.Log("[CustomProtocolHandler] 开始停止IPC监听...");
                    
                    // 取消令牌
                    _cancellationTokenSource?.Cancel();
                    
                    // 等待任务完成（最多等待3秒）
                    if (_ipcListenerTask != null && !_ipcListenerTask.IsCompleted)
                    {
                        try
                        {
                            _ipcListenerTask.Wait(TimeSpan.FromSeconds(3));
                        }
                        catch (AggregateException ex)
                        {
                            // 忽略由于取消导致的异常
                            Debug.Log($"[CustomProtocolHandler] IPC任务取消: {ex.InnerException?.Message}");
                        }
                    }
                    
                    // 清理资源
                    _cancellationTokenSource?.Dispose();
                    _cancellationTokenSource = null;
                    _ipcListenerTask = null;
                    _isIPCInitialized = false;
                    
                    // 取消注册事件
#if UNITY_EDITOR
                    UnityEditor.EditorApplication.playModeStateChanged -= OnPlayModeStateChanged;
#endif
                    Application.quitting -= OnApplicationQuitting;
                    
                    Debug.Log("[CustomProtocolHandler] IPC监听已安全停止");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[CustomProtocolHandler] 停止IPC失败: {ex.Message}");
                }
            }
        }
        #endregion

        #region 协议解析
        /// <summary>
        /// 解析vic://协议URL
        /// </summary>
        public static ChaoXingLoginData ParseProtocolUrl(string protocolUrl)
        {
            if (string.IsNullOrEmpty(protocolUrl) || !protocolUrl.StartsWith("vic://", StringComparison.OrdinalIgnoreCase))
            {
                Debug.LogError("[CustomProtocolHandler] 无效的协议URL");
                return null;
            }

            try
            {
                // 提取查询参数
                int questionMarkIndex = protocolUrl.IndexOf('?');
                if (questionMarkIndex < 0) return null;
                
                string query = protocolUrl.Substring(questionMarkIndex + 1);
                var queryParams = ParseQueryString(query);

                // 验证必要参数
                if (!queryParams.ContainsKey("userId") || !queryParams.ContainsKey("resourceId"))
                {
                    Debug.LogError("[CustomProtocolHandler] 缺少必要参数");
                    return null;
                }

                return new ChaoXingLoginData
                {
                    fid = queryParams.GetValueOrDefault("fid", ""),
                    confSubjectId = queryParams.GetValueOrDefault("confSubjectId", ""),
                    classId = queryParams.GetValueOrDefault("classId", ""),
                    userId = queryParams.GetValueOrDefault("userId", ""),
                    usertype = queryParams.GetValueOrDefault("usertype", ""),
                    resourceId = queryParams.GetValueOrDefault("resourceId", ""),
                    handshake = queryParams.GetValueOrDefault("handshake", ""),
                    mode = queryParams.GetValueOrDefault("mode", ""),
                    timestamp = queryParams.GetValueOrDefault("timestamp", ""),
                    source = "" // 将由ProcessLoginData统一设置
                };
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CustomProtocolHandler] 解析失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 处理协议URL
        /// </summary>
        public static void HandleProtocolUrl(string protocolUrl)
        {
            var loginData = ParseProtocolUrl(protocolUrl);
            if (loginData != null)
            {
                ProcessLoginData(loginData, "command_line");
            }
            else
            {
                Debug.LogError("[CustomProtocolHandler] 协议URL解析失败");
            }
        }

        /// <summary>
        /// 统一处理登录数据的方法
        /// </summary>
        private static void ProcessLoginData(ChaoXingLoginData loginData, string dataSource)
        {
            try
            {
                // 确保数据来源标识正确设置
                loginData.source = dataSource;

                // 统一的数据验证
                if (!loginData.HasValidData())
                {
                    Debug.LogError($"[CustomProtocolHandler] 登录数据验证失败，来源: {dataSource}");
                    return;
                }

                // 统一的日志记录
                Debug.Log($"[CustomProtocolHandler] 处理登录数据，来源: {dataSource}, UserId: {loginData.userId}, ResourceId: {loginData.resourceId}");

                // 存储外部登录信息
                StoredUserId = loginData.userId;
                StoredPersonaId = loginData.resourceId; // 使用resourceId作为personaId

                // 统一的数据设置
                UserDataManager_ChaoXing.SetLoginData(loginData);

                // 触发PassportEvent，确保进入完整的UOS登录流程
                TriggerPassportEvent(true, "超星登录成功", dataSource);

                Debug.Log($"[CustomProtocolHandler] 登录数据处理成功，来源: {dataSource}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CustomProtocolHandler] 处理登录数据异常，来源: {dataSource}, 错误: {ex.Message}");
            }
        }
        #endregion

        #region Unity事件处理
#if UNITY_EDITOR
        private static void OnPlayModeStateChanged(UnityEditor.PlayModeStateChange state)
        {
            if (state == UnityEditor.PlayModeStateChange.ExitingPlayMode)
            {
                Debug.Log("[CustomProtocolHandler] 检测到编辑器退出播放模式，停止IPC监听");
                StopIPCListening();
            }
        }
#endif

        private static void OnApplicationQuitting()
        {
            Debug.Log("[CustomProtocolHandler] 检测到应用退出，停止IPC监听");
            StopIPCListening();
        }
        #endregion

        #region 私有方法
        private static async Task StartIPCListener(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                NamedPipeServerStream pipeServer = null;
                try
                {
                    Debug.Log("[CustomProtocolHandler] 尝试连接到IPC管道...");
                    pipeServer = new NamedPipeServerStream("VICChaoXingPipe", PipeDirection.In, 1, PipeTransmissionMode.Byte, PipeOptions.Asynchronous);
                    
                    await pipeServer.WaitForConnectionAsync(cancellationToken);
                    Debug.Log("[CustomProtocolHandler] IPC管道连接成功");
                    
                    using (var reader = new StreamReader(pipeServer, Encoding.UTF8))
                    {
                        while (pipeServer.IsConnected && !cancellationToken.IsCancellationRequested)
                        {
                            string jsonData = await reader.ReadLineAsync();
                            if (!string.IsNullOrEmpty(jsonData))
                            {
                                Debug.Log($"[CustomProtocolHandler] 接收到IPC数据: {jsonData}");

                                // 安全地调度到主线程
                                try
                                {
                                    var dispatcher = UnityMainThreadDispatcher.Instance();
                                    if (dispatcher != null)
                                    {
                                        dispatcher.Enqueue(() => HandleReceivedAuthData(jsonData));
                                    }
                                    else
                                    {
                                        Debug.LogError("[CustomProtocolHandler] 无法获取主线程调度器");
                                    }
                                }
                                catch (Exception dispatchEx)
                                {
                                    Debug.LogWarning($"[CustomProtocolHandler] 主线程调度失败: {dispatchEx.Message}");
                                }
                            }
                            else
                            {
                                Debug.Log("[CustomProtocolHandler] 接收到空数据或连接断开，继续等待...");
                            }
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    Debug.Log("[CustomProtocolHandler] IPC监听已取消");
                    break;
                }
                catch (ObjectDisposedException)
                {
                    Debug.Log("[CustomProtocolHandler] IPC管道已释放");
                    break;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[CustomProtocolHandler] IPC错误: {ex.Message}");
                }
                finally
                {
                    // 确保管道被正确释放
                    try
                    {
                        pipeServer?.Dispose();
                    }
                    catch (Exception disposeEx)
                    {
                        Debug.LogWarning($"[CustomProtocolHandler] 管道释放异常: {disposeEx.Message}");
                    }
                }

                // 检查是否已被取消，避免不必要的重连
                if (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        await Task.Delay(3000, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        Debug.Log("[CustomProtocolHandler] 重连等待被取消");
                        break;
                    }
                }
            }
            Debug.Log("[CustomProtocolHandler] IPC监听循环结束");
        }

        private static void HandleReceivedAuthData(string jsonData)
        {
            try
            {
                Debug.Log($"[CustomProtocolHandler] 开始处理IPC数据，数据长度: {jsonData?.Length ?? 0}");
                Debug.Log($"[CustomProtocolHandler] IPC数据内容: {jsonData?.Substring(0, Math.Min(200, jsonData?.Length ?? 0))}...");

                var authData = JsonConvert.DeserializeObject<ChaoXingLoginData>(jsonData);
                if (authData != null)
                {
                    Debug.Log($"[CustomProtocolHandler] IPC数据反序列化成功，UserId: {authData.userId}, ResourceId: {authData.resourceId}");
                    // 使用统一的处理方法，标识数据来源为管道
                    ProcessLoginData(authData, "ipc_pipe");
                }
                else
                {
                    Debug.LogError("[CustomProtocolHandler] IPC数据反序列化失败，authData为null");
                }
            }
            catch (JsonException jsonEx)
            {
                Debug.LogError($"[CustomProtocolHandler] JSON反序列化失败: {jsonEx.Message}");
                Debug.LogError($"[CustomProtocolHandler] 原始JSON数据: {jsonData}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CustomProtocolHandler] 处理IPC授权数据失败: {ex.Message}");
                Debug.LogError($"[CustomProtocolHandler] 异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 触发Passport事件，与ChaoXingButton保持一致的处理逻辑
        /// </summary>
        private static void TriggerPassportEvent(bool success, string message, string dataSource)
        {
            try
            {
                if (!success)
                {
                    Debug.LogError($"[CustomProtocolHandler] PassportEvent失败: {message}, 来源: {dataSource}");
                    return;
                }

                // 使用协程延迟执行，确保UI组件已初始化
                var dispatcher = UnityMainThreadDispatcher.Instance();
                if (dispatcher != null)
                {
                    dispatcher.Enqueue(() => 
                    {
                        // 延迟执行，给UI组件初始化时间
                        dispatcher.StartCoroutine(DelayedTriggerPassportEvent(dataSource));
                    });
                }
                else
                {
                    Debug.LogWarning($"[CustomProtocolHandler] 无法获取主线程调度器，尝试直接调用，来源: {dataSource}");
                    DirectTriggerPassportEvent(dataSource);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CustomProtocolHandler] 触发PassportEvent失败，来源: {dataSource}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 延迟触发PassportEvent的协程
        /// </summary>
        private static System.Collections.IEnumerator DelayedTriggerPassportEvent(string dataSource)
        {
            // 等待几帧，确保UI组件完全初始化
            for (int i = 0; i < 5; i++)
            {
                yield return null;
            }
            
            // 额外等待一小段时间
            yield return new UnityEngine.WaitForSeconds(0.1f);
            
            DirectTriggerPassportEvent(dataSource);
        }

        /// <summary>
        /// 直接触发PassportEvent
        /// </summary>
        private static void DirectTriggerPassportEvent(string dataSource)
        {
            try
            {
                var passportController = UnityEngine.Object.FindObjectOfType<VIC.Networking.UOS.UI.PassportUIControllerNova_VICLogin>();
                if (passportController != null)
                {
                    Debug.Log($"[CustomProtocolHandler] 找到PassportController，触发OnExternalLoginSuccess，来源: {dataSource}");
                    passportController.OnExternalLoginSuccess("ChaoXingLogin");
                }
                else
                {
                    Debug.LogWarning($"[CustomProtocolHandler] 未找到PassportUIControllerNova_VICLogin实例，来源: {dataSource}");
                    
                    // 如果是命令行方式，再尝试延迟执行
                    if (dataSource == "command_line")
                    {
                        var dispatcher = UnityMainThreadDispatcher.Instance();
                        if (dispatcher != null)
                        {
                            dispatcher.StartCoroutine(RetryTriggerPassportEvent(dataSource, 3));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CustomProtocolHandler] 直接触发PassportEvent异常，来源: {dataSource}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 重试触发PassportEvent的协程
        /// </summary>
        private static System.Collections.IEnumerator RetryTriggerPassportEvent(string dataSource, int maxRetries)
        {
            for (int retry = 0; retry < maxRetries; retry++)
            {
                yield return new UnityEngine.WaitForSeconds(1f); // 等待1秒后重试
                
                var passportController = UnityEngine.Object.FindObjectOfType<VIC.Networking.UOS.UI.PassportUIControllerNova_VICLogin>();
                if (passportController != null)
                {
                    Debug.Log($"[CustomProtocolHandler] 重试成功，找到PassportController，来源: {dataSource}, 重试次数: {retry + 1}");
                    passportController.OnExternalLoginSuccess("ChaoXingLogin");
                    yield break;
                }
                
                Debug.LogWarning($"[CustomProtocolHandler] 重试 {retry + 1}/{maxRetries} 未找到PassportController，来源: {dataSource}");
            }
            
            Debug.LogError($"[CustomProtocolHandler] 重试 {maxRetries} 次后仍未找到PassportController，来源: {dataSource}");
        }

        private static Dictionary<string, string> ParseQueryString(string query)
        {
            var result = new Dictionary<string, string>();
            foreach (string param in query.Split('&'))
            {
                string[] parts = param.Split('=');
                if (parts.Length == 2)
                {
                    result[WWW.UnEscapeURL(parts[0])] = WWW.UnEscapeURL(parts[1]);
                }
            }
            return result;
        }
        #endregion
    }
}