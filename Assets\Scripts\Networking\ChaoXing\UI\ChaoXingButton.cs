using UnityEngine;
using UnityEngine.UI;
using VIC.Networking.ChaoXing;
using System.Diagnostics;
using NovaSamples.UIControls;
using Nova;
using VIC.Networking.UOS.UI;
using System.Collections;
using Unity.Passport.Runtime.UI;

namespace VIC.UI
{
    /// <summary>
    /// 超星登录按钮组件 - 简化的UI控制器
    /// </summary>
    public class ChaoXingButton : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private UIButton loginButton;
        [SerializeField] private Text buttonText;
        [SerializeField] private NovaSamples.UIControls.Button cancelButton;

        [Header("配置")]
        [SerializeField] private string authUrl = "https://i.mooc.chaoxing.com/";
        [SerializeField] private float loginTimeoutSeconds = 120f;
        [SerializeField] private float cancelButtonDelaySeconds = 15f;
        [SerializeField] private PassportUIControllerNova_VICLogin passportUIController;

        private bool isProcessing = false;
        private float loginStartTime = 0f;

        private void Start()
        {
            InitializeComponents();
            BindEvents();
            UpdateButtonState(false);
        }

        private void OnDestroy()
        {
            try
            {
                UnbindEvents();
                
                // 安全地停止IPC监听
                if (isProcessing)
                {
                    CancelLogin();
                }
                else
                {
                    CustomProtocolHandler.StopIPCListening();
                }
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"[ChaoXingButton] OnDestroy异常: {ex.Message}");
            }
        }

        #region 初始化
        private void InitializeComponents()
        {
            if (loginButton == null) loginButton = GetComponent<UIButton>();
            if (buttonText == null) buttonText = GetComponentInChildren<Text>();

            // 初始化时隐藏取消按钮
            if (cancelButton != null)
            {
                cancelButton.gameObject.SetActive(false);
            }
        }

        private void BindEvents()
        {
            if (loginButton != null)
            {
                loginButton.OnClicked.AddListener(OnLoginButtonClicked);
            }
            if (cancelButton != null)
            {
                cancelButton.OnClicked.AddListener(OnCancelButtonClicked);
            }
            UserDataManager_ChaoXing.OnLoginStatusChanged += OnLoginStatusChanged;
        }

        private void UnbindEvents()
        {
            if (loginButton != null)
            {
                loginButton.OnClicked.RemoveListener(OnLoginButtonClicked);
            }
            if (cancelButton != null)
            {
                cancelButton.OnClicked.RemoveListener(OnCancelButtonClicked);
            }
            UserDataManager_ChaoXing.OnLoginStatusChanged -= OnLoginStatusChanged;
        }
        #endregion

        #region 事件处理
        private void OnLoginButtonClicked()
        {
            if (isProcessing) return;
            StartLoginProcess();
        }

        private void OnCancelButtonClicked()
        {
            if (isProcessing)
            {
                CancelLogin();
            }
        }

        private void OnLoginStatusChanged(bool success, string message)
        {
            UnityEngine.Debug.Log($"[ChaoXingButton] 登录状态变化: {(success ? "成功" : "失败")} - {message}");

            ShowLoadingPanel(false);
            CustomProtocolHandler.StopIPCListening();
            UpdateButtonState(false);

            // 注意：不在这里触发PassportEvent，避免重复调用
            // CustomProtocolHandler已经会触发PassportEvent
            if (success)
            {
                UnityEngine.Debug.Log("[ChaoXingButton] 登录成功，等待CustomProtocolHandler触发PassportEvent");
            }
            else
            {
                OnLoginFailed(message);
            }
        }
        #endregion

        #region 登录流程
        private void StartLoginProcess()
        {
            UnityEngine.Debug.Log("[ChaoXingButton] 开始登录流程");

            ShowLoadingPanel(true);
            UpdateButtonState(true);
            loginStartTime = Time.time;

            try
            {
                CustomProtocolHandler.StartIPCListening();
                OpenUrlInBrowser(authUrl);
                StartCoroutine(MonitorLoginTimeout());
                StartCoroutine(ShowCancelButtonAfterDelay());
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"[ChaoXingButton] 登录流程启动失败: {ex.Message}");
                UpdateButtonState(false);
            }
        }

        private void OnLoginFailed(string errorMessage)
        {
            UnityEngine.Debug.LogError($"[ChaoXingButton] 登录失败: {errorMessage}");
            // 登录失败时可以在这里显示错误信息，但不需要触发PassportEvent
            // 因为CustomProtocolHandler不会在失败时调用TriggerPassportEvent
        }
        #endregion

        #region UI控制
        private void UpdateButtonState(bool processing)
        {
            isProcessing = processing;

            if (loginButton != null)
            {
                var interactable = loginButton.GetComponent<Interactable>();
                if (interactable != null) interactable.enabled = !processing;
            }

            if (buttonText != null)
            {
                buttonText.text = processing ? "登录中..." : "超星登录";
            }

            // 如果停止处理，立即隐藏取消按钮
            if (!processing && cancelButton != null)
            {
                cancelButton.gameObject.SetActive(false);
            }
        }

        private void ShowLoadingPanel(bool show)
        {
            try
            {
                if (passportUIController != null)
                {
                    var loadingMethod = passportUIController.GetType().GetMethod("Loading",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    loadingMethod?.Invoke(passportUIController, new object[] { show });
                }
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"[ChaoXingButton] 控制加载面板失败: {ex.Message}");
            }
        }
        #endregion

        #region 工具方法
        private void OpenUrlInBrowser(string url)
        {
#if UNITY_EDITOR
            Application.OpenURL(url);
#elif UNITY_STANDALONE_WIN
            try
            {
                Process.Start(new ProcessStartInfo { FileName = url, UseShellExecute = true });
            }
            catch
            {
                Application.OpenURL(url);
            }
#else
            Application.OpenURL(url);
#endif
        }

        private System.Collections.IEnumerator MonitorLoginTimeout()
        {
            while (isProcessing)
            {
                try
                {
                    if (Time.time - loginStartTime > loginTimeoutSeconds)
                    {
                        UnityEngine.Debug.LogWarning("[ChaoXingButton] 登录超时");

                        // 设置处理状态为false，防止重复处理
                        isProcessing = false;

                        // 立即停止IPC监听，避免继续尝试连接
                        try
                        {
                            CustomProtocolHandler.StopIPCListening();
                        }
                        catch (System.Exception ex)
                        {
                            UnityEngine.Debug.LogError($"[ChaoXingButton] 停止IPC监听失败: {ex.Message}");
                        }

                        ShowLoadingPanel(false);
                        UpdateButtonState(false);
                        OnLoginFailed("登录超时，请重试");
                        yield break;
                    }

                    if (buttonText != null && isProcessing)
                    {
                        float remainingTime = loginTimeoutSeconds - (Time.time - loginStartTime);
                        int minutes = Mathf.FloorToInt(remainingTime / 60);
                        int seconds = Mathf.FloorToInt(remainingTime % 60);
                        buttonText.text = $"等待登录... {minutes:00}:{seconds:00}";
                    }
                }
                catch (System.Exception ex)
                {
                    UnityEngine.Debug.LogError($"[ChaoXingButton] 超时监控异常: {ex.Message}");
                    isProcessing = false;
                    UpdateButtonState(false);
                    yield break;
                }

                yield return new WaitForSeconds(1f);
            }
        }

        private System.Collections.IEnumerator ShowCancelButtonAfterDelay()
        {
            yield return new WaitForSeconds(cancelButtonDelaySeconds);

            // 只有在仍在处理状态时才显示取消按钮
            if (isProcessing && cancelButton != null)
            {
                cancelButton.gameObject.SetActive(true);
                UnityEngine.Debug.Log($"[ChaoXingButton] {cancelButtonDelaySeconds}秒后显示取消按钮");
            }
        }


        #endregion

        #region 公共方法
        public void SetAuthUrl(string url)
        {
            if (!string.IsNullOrEmpty(url)) authUrl = url;
        }

        public bool IsProcessing() => isProcessing;

        public void CancelLogin()
        {
            if (isProcessing)
            {
                try
                {
                    UnityEngine.Debug.Log("[ChaoXingButton] 取消登录流程");
                    
                    // 先设置状态，防止重复处理
                    isProcessing = false;
                    
                    ShowLoadingPanel(false);
                    
                    // 安全地停止IPC监听
                    try
                    {
                        CustomProtocolHandler.StopIPCListening();
                    }
                    catch (System.Exception ex)
                    {
                        UnityEngine.Debug.LogError($"[ChaoXingButton] 停止IPC监听失败: {ex.Message}");
                    }
                    
                    UpdateButtonState(false);
                    OnLoginFailed("用户取消登录");
                }
                catch (System.Exception ex)
                {
                    UnityEngine.Debug.LogError($"[ChaoXingButton] 取消登录异常: {ex.Message}");
                    // 确保状态被重置
                    isProcessing = false;
                    UpdateButtonState(false);
                }
            }
        }
        #endregion
    }
}
