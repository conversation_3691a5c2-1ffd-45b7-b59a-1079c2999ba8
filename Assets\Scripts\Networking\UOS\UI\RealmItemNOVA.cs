/* ===========
 * 作者: <PERSON><PERSON><PERSON><PERSON>
 * 创建时间: 2025/05/12
 * 非作者最新修改时间：  
 * =========== */
using Passport;
using UnityEngine;
using TMPro;
using System.Collections.Generic;
using Nova;
using NovaSamples.UIControls;


namespace VIC.Networking.UOS.UI
{
    public class RealmItemNOVA : MonoBehaviour
    {
        public TextBlock realmName;
        public List<Sprite> spriteList;
        // public TextMeshProUGUI id;
        public UIBlock2D realmImage;
        private Realm _realm;
        public GameObject focus;

        public void Reset()
        {
            realmName = transform.Find("Name").GetComponent<TextBlock>();
            realmImage = transform.Find("Item").gameObject.GetComponent<UIBlock2D>();
            focus = transform.Find("Focus").gameObject;
            // id = transform.Find("ID").GetComponent<TextMeshProUGUI>();
        }
        
        public void Set(Realm realm)
        {
            realmName.Text = realm.Name;
            // id.text = realm.RealmID;
            _realm = realm;
            // 随机获取一个图标
            var index = Random.Range(0, spriteList.Count);
            Sprite sprite = spriteList[index];
            realmImage.SetImage(sprite);
        }

        public void Select()
        {
            DemoUIControllerNOVA.SelectRealm.Invoke(_realm);
        }

    }
}