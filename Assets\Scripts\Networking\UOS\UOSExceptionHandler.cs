/* ===========
 * 作者: AI Assistant
 * 创建时间: 2025/01/22
 * 描述: UOS系统统一异常处理工具类
 * =========== */
using System;
using UnityEngine;
using Unity.Passport.Runtime.Model;
using VIC.UI;
using Unity.Passport.Runtime.UI;
using Unity.Passport.Runtime;

namespace VIC.Networking.UOS
{
    /// <summary>
    /// UOS系统统一异常处理工具类
    /// 提供标准化的异常处理和错误消息显示
    /// </summary>
    public static class UOSExceptionHandler
    {
        #region 异常类型检查

        /// <summary>
        /// 检查是否为认证相关异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>是否为认证异常</returns>
        public static bool IsAuthenticationException(Exception ex)
        {
            if (ex == null) return false;
            
            string message = ex.Message;
            return message.Contains("未找到User") ||
                   message.Contains("Unauthenticated") ||
                   message.Contains("需要重新登录") ||
                   message.Contains("AccessToken") ||
                   message.Contains("RefreshToken") ||
                   message.Contains("NeedLogin") ||
                   message.Contains("Refresh Token已过期");
        }

        /// <summary>
        /// 检查是否为CloudSave客户端异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>是否为CloudSave客户端异常</returns>
        public static bool IsCloudSaveClientException(Exception ex)
        {
            return ex?.GetType().Name.Contains("CloudSaveClientException") == true;
        }

        /// <summary>
        /// 检查是否为CloudSave服务器异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>是否为CloudSave服务器异常</returns>
        public static bool IsCloudSaveServerException(Exception ex)
        {
            return ex?.GetType().Name.Contains("CloudSaveServerException") == true;
        }

        #endregion

        #region 标准化异常处理

        /// <summary>
        /// 处理PassportException
        /// </summary>
        /// <param name="ex">Passport异常</param>
        /// <param name="context">上下文信息</param>
        /// <param name="showUI">是否显示UI消息</param>
        public static void HandlePassportException(PassportException ex, string context = "", bool showUI = true)
        {
            if (ex == null) return;

            string logMessage = string.IsNullOrEmpty(context) 
                ? $"Passport异常: {ex.Code}" 
                : $"{context} - Passport异常: {ex.Code}";
            
            Debug.LogError(logMessage);

            if (showUI)
            {
                UIMessage.Show(ex);
            }
        }

        /// <summary>
        /// 处理认证相关异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="context">上下文信息</param>
        /// <param name="showUI">是否显示UI消息</param>
        /// <returns>是否需要重新登录</returns>
        public static bool HandleAuthenticationException(Exception ex, string context = "", bool showUI = true)
        {
            if (ex == null || !IsAuthenticationException(ex)) return false;

            string logMessage = string.IsNullOrEmpty(context)
                ? $"认证异常: {ex.Message}"
                : $"{context} - 认证异常: {ex.Message}";
            
            Debug.LogWarning(logMessage);

            if (showUI)
            {
                UIMessage.Show("认证已过期，正在尝试重新登录...", MessageType.Error);
            }

            return true;
        }

        /// <summary>
        /// 处理CloudSave异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="context">上下文信息</param>
        /// <param name="showUI">是否显示UI消息</param>
        public static void HandleCloudSaveException(Exception ex, string context = "", bool showUI = true)
        {
            if (ex == null) return;

            string exceptionType = "未知";
            if (IsCloudSaveClientException(ex))
                exceptionType = "客户端";
            else if (IsCloudSaveServerException(ex))
                exceptionType = "服务器";

            string logMessage = string.IsNullOrEmpty(context)
                ? $"CloudSave{exceptionType}异常: {ex.Message}"
                : $"{context} - CloudSave{exceptionType}异常: {ex.Message}";
            
            Debug.LogError(logMessage);

            if (showUI && !IsAuthenticationException(ex))
            {
                UIMessage.Show($"云存储操作失败: {ex.Message}", MessageType.Error);
            }
        }

        /// <summary>
        /// 处理一般异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="context">上下文信息</param>
        /// <param name="showUI">是否显示UI消息</param>
        public static void HandleGeneralException(Exception ex, string context = "", bool showUI = true)
        {
            if (ex == null) return;

            string logMessage = string.IsNullOrEmpty(context)
                ? $"异常: {ex.Message}"
                : $"{context} - 异常: {ex.Message}";
            
            Debug.LogError(logMessage);

            if (showUI)
            {
                UIMessage.Show($"操作失败: {ex.Message}", MessageType.Error);
            }
        }

        #endregion

        #region 统一异常处理入口

        /// <summary>
        /// 统一异常处理入口
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="context">上下文信息</param>
        /// <param name="showUI">是否显示UI消息</param>
        /// <returns>是否需要重新登录</returns>
        public static bool HandleException(Exception ex, string context = "", bool showUI = true)
        {
            if (ex == null) return false;

            // 优先处理认证异常
            if (IsAuthenticationException(ex))
            {
                return HandleAuthenticationException(ex, context, showUI);
            }

            // 处理Passport异常
            if (ex is PassportException passportEx)
            {
                HandlePassportException(passportEx, context, showUI);
                return false;
            }

            // 处理CloudSave异常
            if (IsCloudSaveClientException(ex) || IsCloudSaveServerException(ex))
            {
                HandleCloudSaveException(ex, context, showUI);
                return false;
            }

            // 处理一般异常
            HandleGeneralException(ex, context, showUI);
            return false;
        }

        #endregion

        #region 缓存清理统一接口

        /// <summary>
        /// 统一的缓存清理接口
        /// </summary>
        /// <param name="context">清理上下文</param>
        /// <param name="clearLoginCaches">是否清理登录相关缓存</param>
        public static void ClearCaches(string context = "", bool clearLoginCaches = false)
        {
            try
            {
                if (clearLoginCaches)
                {
                    VIC.Utils.CacheManager.ClearAllCaches(context);
                }
                else
                {
                    // 只清理UOS相关缓存，不清理登录信息
                    UserDataManager_UOS.ClearAllCaches();
                }

                Debug.Log($"缓存清理完成 - {context}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"缓存清理失败 - {context}: {ex.Message}");
            }
        }

        #endregion
    }
}
