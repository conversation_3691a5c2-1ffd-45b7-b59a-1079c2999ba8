/* ===========  
 * 作者: Zhu<PERSON><PERSON>ong  
 * 创建时间: 2025/05/28 
 * 非作者最新修改时间：  
 * ===========  
 */
using System;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using Economy;
using Nova;
using Unity.Passport.Runtime;
using Unity.Passport.Runtime.Model;
using Unity.UOS.Common.UOSLauncher.Scripts.Auth;
using UnityEngine;
using VIC.Core;
using VIC.Launcher.Profile;
using static VIC.Game.Managers.GameLibraryManager;

/// <summary>
/// UOS交易系统管理类，提供与Unity Online Services经济系统交互的功能
/// </summary>
public static class TradingSystemManagement_UOS
{
    #region 私有字段

    private static ListPersonaInventoryResponse _personaInventories;

    // 商品缓存相关
    private static Dictionary<string, ExpandedProductSDK> _cachedProducts = new Dictionary<string, ExpandedProductSDK>();
    private static Dictionary<string, ProductDisplayInfo> _cachedProductDisplayInfo = new Dictionary<string, ProductDisplayInfo>();
    private static DateTime _lastProductCacheUpdate = DateTime.MinValue;
    private static readonly TimeSpan _cacheExpireTime = TimeSpan.FromMinutes(10); // 缓存10分钟过期

    // 防止重复刷新的锁和标志
    private static bool _isRefreshingCache = false;
    private static readonly object _refreshLock = new object();

    // 防止重复获取背包的锁和标志
    private static bool _isRefreshingInventory = false;
    private static readonly object _inventoryLock = new object();

    #endregion

    #region 数据结构

    /// <summary>
    /// 商品显示信息，用于商店页面展示
    /// </summary>
    [Serializable]
    public class ProductDisplayInfo
    {
        /// <summary>
        /// 商品显示名称
        /// </summary>
        public string DisplayName;

        /// <summary>
        /// 商品唯一标识
        /// </summary>
        public string SlugName;

        /// <summary>
        /// 购买该商品需要消耗的物品或货币详情
        /// </summary>
        public List<ProductCostInfo> Costs;

        /// <summary>
        /// 是否可购买
        /// </summary>
        public bool Purchasable;

        /// <summary>
        /// 商品自定义属性
        /// </summary>
        public Dictionary<string, string> CustomData;
    }

    /// <summary>
    /// 商品消费信息，简化的ProductEntityDetail用于显示
    /// </summary>
    [Serializable]
    public class ProductCostInfo
    {
        /// <summary>
        /// 资源唯一标识
        /// </summary>
        public string ResourceSlug;

        /// <summary>
        /// 资源显示名称
        /// </summary>
        public string DisplayName;

        /// <summary>
        /// 需要消耗的数量
        /// </summary>
        public uint Quantity;

        /// <summary>
        /// 资源类型
        /// </summary>
        public Economy.ResourceType ResourceType;
    }

    #endregion

    #region 初始化

    /// <summary>
    /// 初始化交易系统
    /// </summary>
    /// <returns>初始化操作的任务</returns>
    public static async UniTask Initialize()
    {
        await InitializeTradingSystem();
    }

    /// <summary>
    /// 初始化交易系统的内部实现
    /// </summary>
    private static async UniTask InitializeTradingSystem()
    {
        try
        {
            // 移除PassportFeatureSDK.Initialize()调用，由InitializationManager_UOS统一管理
            Debug.Log("交易系统初始化成功");
        }
        catch (Exception ex)
        {
            Debug.LogWarning($"交易系统初始化失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 确保PassportFeatureSDK已正确初始化
    /// </summary>
    private static async UniTask EnsureInitialized()
    {
        try
        {
            // 检查InitializationManager中PassportFeatureSDK的初始化状态
            if (InitializationManager_UOS.Instance != null)
            {
                var passportState = InitializationManager_UOS.Instance.GetInitState("PassportFeatureSDK");
                if (passportState != InitializationManager_UOS.InitState.Completed)
                {
                    throw new InvalidOperationException($"PassportFeatureSDK未正确初始化，当前状态: {passportState}");
                }
            }
            else
            {
                throw new InvalidOperationException("InitializationManager_UOS实例不存在");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"PassportFeatureSDK初始化检查失败: {ex.Message}");
            throw new InvalidOperationException("交易系统未正确初始化", ex);
        }
    }

    #endregion

    #region 游戏管理

    /// <summary>
    /// 异步加载用户拥有的游戏（仅从UOS背包中读取）
    /// </summary>
    public static async UniTask LoadOwnedGamesFromInventoryOnlyAsync(
        Dictionary<GameProfile, GameState> gameStates,
        HashSet<GameProfile> ownedGames,
        HashSet<GameProfile> allGames)
    {
        try
        {
            // 从UOS背包中加载已拥有的资源
            var inventoryResponse = await GetPersonaInventory();

            if (inventoryResponse?.Inventory == null || inventoryResponse.Inventory.Count == 0)
            {
                Debug.LogWarning("UOS背包中未找到任何资源");
                return;
            }


            // 构建 DisplayName -> GameProfile 的映射（注意区分大小写）
            var nameToProfile = new Dictionary<string, GameProfile>();
            foreach (var game in allGames)
            {
                if (!string.IsNullOrEmpty(game.GameName))
                {
                    nameToProfile[game.GameName] = game;
                }
            }

            var matchedNames = new List<string>();

            // 打印所有背包资源名称并匹配游戏
            foreach (var item in inventoryResponse.Inventory)
            {

                string displayName = item.Resource?.DisplayName;
                if (string.IsNullOrEmpty(displayName)) continue;

                if (nameToProfile.TryGetValue(displayName, out var profile))
                {
                    matchedNames.Add(displayName);
                }
                else
                {
                    Debug.LogWarning($"背包资源 {displayName} 未能匹配到本地 GameProfile");
                }
            }

            // 更新本地状态
            lock (gameStates)
            {
                ownedGames.Clear();
                foreach (var name in matchedNames)
                {
                    if (nameToProfile.TryGetValue(name, out var profile))
                    {
                        ownedGames.Add(profile);
                    }
                }
            }

        }
        catch (Exception ex)
        {
            Debug.LogError($"从UOS背包加载失败: {ex.Message}");
        }
    }

    #endregion

    #region 商品管理

    /// <summary>
    /// 获取所有商品的显示信息（带缓存）
    /// </summary>
    /// <param name="categorySlug">商品目录slug，默认为"BuyGames"</param>
    /// <param name="forceRefresh">是否强制刷新缓存</param>
    /// <returns>商品显示信息列表</returns>
    public static async UniTask<List<ProductDisplayInfo>> GetAllProductDisplayInfoAsync(string categorySlug = "BuyGames", bool forceRefresh = false)
    {
        try
        {
            // 确保交易系统已初始化
            await EnsureInitialized();

            // 检查缓存是否过期或需要强制刷新
            bool needRefresh = forceRefresh || _cachedProductDisplayInfo.Count == 0 ||
                DateTime.Now - _lastProductCacheUpdate > _cacheExpireTime;
            if (needRefresh)
            {
                // 使用锁防止多个并发刷新
                lock (_refreshLock)
                {
                    // 双重检查，防止在等待锁的过程中其他线程已经刷新了缓存
                    if (forceRefresh || _cachedProductDisplayInfo.Count == 0 ||
                        DateTime.Now - _lastProductCacheUpdate > _cacheExpireTime)
                    {
                        if (!_isRefreshingCache)
                        {
                            _isRefreshingCache = true;
                            // 在锁外执行异步刷新
                            _ = RefreshProductCacheAsync(categorySlug);
                        }
                    }
                }

                // 等待刷新完成（最多等待10秒）
                int waitCount = 0;
                while (_isRefreshingCache && waitCount < 100)
                {
                    await UniTask.Delay(100);
                    waitCount++;
                }

                // Debug.Log($"[TradingSystem] 缓存刷新完成，等待了 {waitCount * 100}ms");
            }

            var result = _cachedProductDisplayInfo.Values.ToList();
            // Debug.Log($"[TradingSystem] 返回商品列表，数量: {result.Count}");

            return result;
        }
        catch (Exception ex)
        {
            Debug.LogError($"[TradingSystem] 获取商品显示信息失败: {ex.Message}");
            Debug.LogError($"[TradingSystem] 异常堆栈: {ex.StackTrace}");
            return new List<ProductDisplayInfo>();
        }
    }

    /// <summary>
    /// 获取指定商品的显示信息
    /// </summary>
    /// <param name="slugName">商品唯一标识</param>
    /// <returns>商品显示信息，未找到时返回null</returns>
    public static ProductDisplayInfo GetProductDisplayInfo(string slugName)
    {
        if (string.IsNullOrEmpty(slugName))
        {
            Debug.LogWarning("获取商品显示信息失败: slugName不能为空");
            return null;
        }

        _cachedProductDisplayInfo.TryGetValue(slugName, out var displayInfo);
        return displayInfo;
    }

    /// <summary>
    /// 获取指定商品的消费信息
    /// </summary>
    /// <param name="slugName">商品唯一标识</param>
    /// <returns>商品消费信息列表</returns>
    public static List<ProductCostInfo> GetProductCosts(string slugName)
    {
        var displayInfo = GetProductDisplayInfo(slugName);
        return displayInfo?.Costs ?? new List<ProductCostInfo>();
    }

    /// <summary>
    /// 获取商品的格式化价格信息（用于UI显示）
    /// </summary>
    /// <param name="slugName">商品唯一标识</param>
    /// <returns>格式化的价格字符串，例如："100 金币"</returns>
    public static string GetFormattedProductPrice(string slugName)
    {
        var costs = GetProductCosts(slugName);
        if (costs == null || costs.Count == 0)
        {
            return "免费";
        }

        var priceStrings = new List<string>();
        foreach (var cost in costs)
        {
            priceStrings.Add($"{cost.Quantity} {cost.DisplayName}");
        }

        return string.Join(" + ", priceStrings);
    }

    /// <summary>
    /// 检查玩家是否有足够的资源购买指定商品
    /// </summary>
    /// <param name="slugName">商品唯一标识</param>
    /// <returns>是否有足够资源购买</returns>
    public static bool CanAffordProduct(string slugName)
    {
        var costs = GetProductCosts(slugName);
        if (costs == null || costs.Count == 0)
        {
            return true; // 免费商品
        }

        foreach (var cost in costs)
        {
            var playerQuantity = GetItemQuantity(cost.ResourceSlug);
            if (playerQuantity < cost.Quantity)
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 清空商品缓存
    /// </summary>
    public static void ClearProductCache()
    {
        _cachedProducts.Clear();
        _cachedProductDisplayInfo.Clear();
        _lastProductCacheUpdate = DateTime.MinValue;
        // Debug.Log("商品缓存已清空");
    }

    /// <summary>
    /// 清空背包缓存
    /// </summary>
    public static void ClearInventoryCache()
    {
        _personaInventories = null;
        // Debug.Log("背包缓存已清空");
    }

    /// <summary>
    /// 清空所有缓存（用于账号切换）
    /// </summary>
    public static void ClearAllCaches()
    {
        ClearProductCache();
        ClearInventoryCache();
        // Debug.Log("交易系统所有缓存已清空");
    }

    /// <summary>
    /// 获取缓存状态信息
    /// </summary>
    /// <returns>缓存状态信息</returns>
    public static string GetCacheStatus()
    {
        var timeSinceUpdate = DateTime.Now - _lastProductCacheUpdate;
        return $"缓存商品数量: {_cachedProductDisplayInfo.Count}, 上次更新: {timeSinceUpdate.TotalMinutes:F1} 分钟前";
    }

    /// <summary>
    /// 刷新商品缓存
    /// </summary>
    /// <param name="categorySlug">商品目录slug</param>
    private static async UniTask RefreshProductCacheAsync(string categorySlug = "BuyGames")
    {
        try
        {
            Debug.Log($"开始刷新商品缓存，目录: {categorySlug}");

            // 确保交易系统已初始化
            await EnsureInitialized();

            // 获取商品列表
            var productListResponse = await PassportFeatureSDK.Economy.ListProducts(categorySlug, 0, 100);

            if (productListResponse?.Products == null || productListResponse.Products.Count == 0)
            {
                Debug.LogWarning("未能获取商品列表或商品数量为0");
                return;
            }

            // 清空旧缓存
            _cachedProducts.Clear();
            _cachedProductDisplayInfo.Clear();

            // 获取每个商品的详细信息并缓存
            foreach (var product in productListResponse.Products)
            {
                try
                {
                    // Debug.Log($"[TradingSystem] 获取商品详情: SlugName='{product.SlugName}', DisplayName='{product.DisplayName}'");
                    var productDetail = await PassportFeatureSDK.Economy.GetProduct(product.SlugName);
                    if (productDetail != null)
                    {
                        // 缓存完整商品信息
                        _cachedProducts[product.SlugName] = productDetail;

                        // 创建并缓存显示信息
                        var displayInfo = CreateProductDisplayInfo(productDetail);
                        _cachedProductDisplayInfo[product.SlugName] = displayInfo;

                        // Debug.Log($"[TradingSystem] 商品缓存成功: DisplayName='{displayInfo.DisplayName}', SlugName='{displayInfo.SlugName}', Purchasable={displayInfo.Purchasable}");
                    }
                    else
                    {
                        Debug.LogWarning($"[TradingSystem] 获取商品详情返回null: {product.SlugName}");
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogWarning($"[TradingSystem] 获取商品 {product.SlugName} 详情失败: {ex.Message}");
                }
            }

            _lastProductCacheUpdate = DateTime.Now;
            // Debug.Log($"商品缓存刷新完成，缓存了 {_cachedProductDisplayInfo.Count} 个商品");
        }
        catch (Exception ex)
        {
            Debug.LogError($"刷新商品缓存失败: {ex.Message}");
        }
        finally
        {
            // 无论成功还是失败，都要重置刷新标志
            _isRefreshingCache = false;
        }
    }

    /// <summary>
    /// 从ExpandedProductSDK创建ProductDisplayInfo
    /// </summary>
    /// <param name="product">完整商品信息</param>
    /// <returns>商品显示信息</returns>
    private static ProductDisplayInfo CreateProductDisplayInfo(ExpandedProductSDK product)
    {
        var displayInfo = new ProductDisplayInfo
        {
            DisplayName = product.DisplayName,
            SlugName = product.SlugName,
            Purchasable = product.Purchasable,
            CustomData = product.CustomData?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
            Costs = new List<ProductCostInfo>()
        };

        // 转换Costs信息
        if (product.Costs != null)
        {
            foreach (var cost in product.Costs)
            {
                var costInfo = new ProductCostInfo
                {
                    ResourceSlug = cost.SlugName,
                    DisplayName = cost.DisplayName,
                    Quantity = cost.Quantity,
                    ResourceType = cost.ResourceType
                };
                displayInfo.Costs.Add(costInfo);
            }
        }

        return displayInfo;
    }

    /// <summary>
    /// 通过商品显示名称购买商品（优先使用缓存）
    /// </summary>
    /// <param name="partialName">商品名称关键字（部分匹配）</param>
    /// <returns>交易信息，失败时返回null</returns>
    public static async UniTask<Transaction> PurchaseProductByPartialName(string partialName)
    {
        if (string.IsNullOrEmpty(partialName))
        {
            Debug.LogError("购买商品失败: 商品名称不能为空");
            return null;
        }

        try
        {
            Debug.Log($"开始模糊搜索并购买商品，关键字: {partialName}");

            // 首先尝试从缓存中查找
            var cachedProducts = await GetAllProductDisplayInfoAsync();

            foreach (var product in cachedProducts)
            {
                if (string.IsNullOrEmpty(product.DisplayName)) continue;

                if (product.DisplayName.IndexOf(partialName, StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    Debug.Log($"从缓存中找到匹配商品: {product.DisplayName}，SlugName: {product.SlugName}，准备购买...");
                    return await BuyItem(product.SlugName, 1);
                }
            }

            // 如果缓存中没有找到，尝试直接从API获取（作为备用方案）
            Debug.Log("缓存中未找到匹配商品，尝试直接从API获取...");
            
            // 确保交易系统已初始化
            await EnsureInitialized();
            
            var productListResponse = await PassportFeatureSDK.Economy.ListProducts("BuyGames", 0, 50);

            if (productListResponse?.Products == null || productListResponse.Products.Count == 0)
            {
                Debug.LogError("未能获取商品列表或商品数量为0");
                return null;
            }

            Debug.Log($"从API获取到 {productListResponse.Products.Count} 个商品");

            // 遍历查找匹配的商品（忽略大小写）
            foreach (var product in productListResponse.Products)
            {
                if (string.IsNullOrEmpty(product.DisplayName)) continue;

                if (product.DisplayName.IndexOf(partialName, StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    Debug.Log($"从API中找到匹配商品: {product.DisplayName}，SlugName: {product.SlugName}，准备购买...");
                    return await BuyItem(product.SlugName, 1);
                }
            }

            Debug.LogWarning($"未在商品列表中找到包含关键字 \"{partialName}\" 的商品");
        }
        catch (Exception ex)
        {
            Debug.LogError($"购买商品发生异常: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// 获取商品详情（优先使用缓存）
    /// </summary>
    /// <param name="slugName">商品的唯一标识slug</param>
    /// <param name="useCache">是否使用缓存，默认为true</param>
    /// <returns>商品详细信息</returns>
    public static async UniTask<ExpandedProductSDK> GetProduct(string slugName, bool useCache = true)
    {
        if (string.IsNullOrEmpty(slugName))
        {
            Debug.LogWarning("获取商品详情失败: slugName不能为空");
            return null;
        }

        try
        {
            // 如果使用缓存且缓存中有数据，直接返回缓存的数据
            if (useCache && _cachedProducts.TryGetValue(slugName, out var cachedProduct))
            {
                Debug.Log($"从缓存中获取商品详情: {slugName}");
                return cachedProduct;
            }

            // 从API获取商品详情
            Debug.Log($"从API获取商品详情: {slugName}");
            
            // 确保交易系统已初始化
            await EnsureInitialized();
            
            var product = await PassportFeatureSDK.Economy.GetProduct(slugName);

            // 如果获取成功，更新缓存
            if (product != null && useCache)
            {
                _cachedProducts[slugName] = product;
                var displayInfo = CreateProductDisplayInfo(product);
                _cachedProductDisplayInfo[slugName] = displayInfo;
            }

            return product;
        }
        catch (Exception ex)
        {
            Debug.LogWarning($"获取商品详情失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 虚拟购买商品
    /// </summary>
    /// <param name="productSlug">商品的唯一标识slug</param>
    /// <param name="quantity">购买的商品的数量，不可为0</param>
    /// <param name="customData">自定义属性</param>
    /// <returns>交易信息</returns>
    private static async UniTask<Transaction> BuyItem(string productSlug, uint quantity, Dictionary<string, string> customData = null)
    {
        if (string.IsNullOrEmpty(productSlug))
        {
            Debug.LogWarning("购买商品失败: productSlug不能为空");
            return null;
        }

        if (quantity == 0)
        {
            Debug.LogWarning("购买商品失败: 购买数量不能为0");
            return null;
        }

        try
        {
            // 确保交易系统已初始化
            await EnsureInitialized();
            
            Transaction transaction = await PassportFeatureSDK.Economy.VirtualPurchase(productSlug, quantity, customData);
            // 购买成功后刷新背包
            await RefreshPersonaInventory();

            uint newAmount = GetItemQuantity("GOLD_COIN");
            EconomyEvents.RaiseGOLD_COINChanged(newAmount);

            return transaction;
        }
        catch (AuthException ex) when (ex.ErrorCode == ErrorCode.NeedLogin)
        {
            Debug.LogWarning("[TradingSystemManagement_UOS] 购买商品时需要重新登录");
            return null;
        }
        catch (Exception ex)
        {
            Debug.LogWarning($"购买商品失败: {ex.Message}");
            return null;
        }
    }

    #endregion

    #region 背包管理

    /// <summary>
    /// 强制刷新角色背包资源
    /// </summary>
    /// <param name="resourceNamespace">背包内资源的自定义命名空间</param>
    /// <returns>最新的角色背包资源列表</returns>
    public static async UniTask<ListPersonaInventoryResponse> RefreshPersonaInventory(string resourceNamespace = null)
    {
        // 强制刷新：清空缓存并重新获取
        _personaInventories = null;

        // 使用锁防止多个并发刷新
        lock (_inventoryLock)
        {
            if (!_isRefreshingInventory)
            {
                _isRefreshingInventory = true;
                // 在锁外执行异步获取
                _ = FetchPersonaInventoryAsync(resourceNamespace);
            }
        }

        // 等待获取完成（最多等待10秒）
        int waitCount = 0;
        while (_isRefreshingInventory && waitCount < 100)
        {
            await UniTask.Delay(100);
            waitCount++;
        }

        return _personaInventories;
    }

    /// <summary>
    /// 获取角色背包内所有资源
    /// </summary>
    /// <param name="resourceNamespace">背包内资源的自定义命名空间</param>
    /// <returns>角色背包资源列表</returns>
    public static async UniTask<ListPersonaInventoryResponse> GetPersonaInventory(string resourceNamespace = null)
    {
        // 如果已经有缓存且不是强制刷新，直接返回缓存
        if (_personaInventories != null)
        {
            return _personaInventories;
        }

        // 使用锁防止多个并发获取
        lock (_inventoryLock)
        {
            // 双重检查，防止在等待锁的过程中其他线程已经获取了背包
            if (_personaInventories != null)
            {
                return _personaInventories;
            }

            if (!_isRefreshingInventory)
            {
                _isRefreshingInventory = true;
                // 在锁外执行异步获取
                _ = FetchPersonaInventoryAsync(resourceNamespace);
            }
        }

        // 等待获取完成（最多等待10秒）
        int waitCount = 0;
        while (_isRefreshingInventory && waitCount < 100)
        {
            await UniTask.Delay(100);
            waitCount++;
        }

        return _personaInventories;
    }

    /// <summary>
    /// 实际获取背包数据的方法
    /// </summary>
    /// <param name="resourceNamespace">背包内资源的自定义命名空间</param>
    private static async UniTask FetchPersonaInventoryAsync(string resourceNamespace = null)
    {
        try
        {
            Debug.Log("开始获取角色背包资源");
            
            // 确保交易系统已初始化
            await EnsureInitialized();
            
            _personaInventories = await PassportFeatureSDK.Economy.ListPersonaInventory(resourceNamespace);
            Debug.Log($"角色背包资源获取完成，共 {_personaInventories?.Inventory?.Count ?? 0} 个资源");

            // 触发背包加载完成事件
            EconomyEvents.RaiseInventoryLoaded();

            // 同时触发金币数量更新事件
            uint goldAmount = GetItemQuantity("GOLD_COIN");
            EconomyEvents.RaiseGOLD_COINChanged(goldAmount);
        }
        catch (AuthException ex) when (ex.ErrorCode == ErrorCode.NeedLogin)
        {
            Debug.LogWarning("[TradingSystemManagement_UOS] 获取角色背包资源时需要重新登录");
            _personaInventories = null;
        }
        catch (Exception ex)
        {
            Debug.LogWarning($"获取角色背包资源失败: {ex.Message}");
            _personaInventories = null;
        }
        finally
        {
            // 无论成功还是失败，都要重置刷新标志
            _isRefreshingInventory = false;
        }
    }

    /// <summary>
    /// 搜索角色背包内资源
    /// </summary>
    /// <param name="start">查询起始位置，默认为0</param>
    /// <param name="count">查询获取的数量，默认为10，最大为50</param>
    /// <param name="resourceNamespace">背包内资源的自定义命名空间</param>
    /// <param name="resourceSlug">需要查询的资源唯一标识slug</param>
    /// <returns>背包内资源搜索结果</returns>
    public static async UniTask<GetPersonaInventoryResponse> SearchPersonaInventory(uint start = 0, uint count = 10,
        string resourceNamespace = null, string resourceSlug = null)
    {
        try
        {
            // 确保交易系统已初始化
            await EnsureInitialized();
            
            return await PassportFeatureSDK.Economy.SearchPersonaInventory(start, count, resourceNamespace, resourceSlug);
        }
        catch (AuthException ex) when (ex.ErrorCode == ErrorCode.NeedLogin)
        {
            Debug.LogWarning("[TradingSystemManagement_UOS] 搜索角色背包资源时需要重新登录");
            return null;
        }
        catch (Exception ex)
        {
            Debug.LogWarning($"搜索角色背包资源失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 消耗角色背包内资源
    /// </summary>
    /// <param name="inventoryItems">需要消耗的背包资源ID及数量</param>
    /// <returns>更新后的相关背包内资源信息</returns>
    public static async UniTask<ConsumeInventoryItemsResponse> ConsumeInventoryItems(Dictionary<string, uint> inventoryItems)
    {
        if (inventoryItems == null || inventoryItems.Count == 0)
        {
            Debug.LogWarning("消耗背包资源失败: 资源列表为空");
            return null;
        }

        try
        {
            // 确保交易系统已初始化
            await EnsureInitialized();
            
            ConsumeInventoryItemsResponse response = await PassportFeatureSDK.Economy.ConsumeInventoryItems(inventoryItems);
            // 消耗成功后刷新背包
            await RefreshPersonaInventory();
            return response;
        }
        catch (AuthException ex) when (ex.ErrorCode == ErrorCode.NeedLogin)
        {
            Debug.LogWarning("[TradingSystemManagement_UOS] 消耗背包资源时需要重新登录");
            return null;
        }
        catch (Exception ex)
        {
            Debug.LogWarning($"消耗背包资源失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 增加角色背包内资源（需在app下开启「允许客户端增加背包资源」配置）
    /// </summary>
    /// <param name="depositResourcesRequest">需要增加的资源信息</param>
    /// <returns>更新后的相关背包内资源信息</returns>
    public static async UniTask<DepositResourcesResponse> DepositResources(List<DepositResource> depositResourcesRequest)
    {
        if (depositResourcesRequest == null || depositResourcesRequest.Count == 0)
        {
            Debug.LogWarning("增加背包资源失败: 资源列表为空");
            return null;
        }

        try
        {
            // 确保交易系统已初始化
            await EnsureInitialized();
            
            DepositResourcesResponse response = await PassportFeatureSDK.Economy.DepositResources(depositResourcesRequest);
            // 增加资源成功后刷新背包
            await RefreshPersonaInventory();
            return response;
        }
        catch (AuthException ex) when (ex.ErrorCode == ErrorCode.NeedLogin)
        {
            Debug.LogWarning("[TradingSystemManagement_UOS] 增加背包资源时需要重新登录");
            return null;
        }
        catch (Exception ex)
        {
            Debug.LogWarning($"增加背包资源失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 更新背包资源自定义属性
    /// </summary>
    /// <param name="inventoryItemId">背包资源ID</param>
    /// <param name="customData">自定义属性，当传入空字典时会更新为空，传入NULL时不更新</param>
    /// <returns>更新后的背包资源</returns>
    public static async UniTask<UpdateInventoryItemResponseSDK> UpdateInventoryItem(string inventoryItemId, Dictionary<string, string> customData = null)
    {
        if (string.IsNullOrEmpty(inventoryItemId))
        {
            Debug.LogWarning("更新背包资源属性失败: 资源ID不能为空");
            return null;
        }

        try
        {
            // 确保交易系统已初始化
            await EnsureInitialized();
            
            UpdateInventoryItemResponseSDK response = await PassportFeatureSDK.Economy.UpdateInventoryItem(inventoryItemId, customData);
            // 更新成功后刷新背包
            await RefreshPersonaInventory();
            return response;
        }
        catch (AuthException ex) when (ex.ErrorCode == ErrorCode.NeedLogin)
        {
            Debug.LogWarning("[TradingSystemManagement_UOS] 更新背包资源属性时需要重新登录");
            return null;
        }
        catch (Exception ex)
        {
            Debug.LogWarning($"更新背包资源属性失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 查询背包内是否有指定物品（通过ResourceSlug）
    /// </summary>
    /// <param name="resourceSlug">资源唯一标识</param>
    /// <returns>是否拥有该物品</returns>
    public static bool HasItem(string resourceSlug)
    {
        if (string.IsNullOrEmpty(resourceSlug))
        {
            Debug.LogWarning("查询背包物品失败: 资源标识不能为空");
            return false;
        }

        if (_personaInventories?.Inventory == null)
            return false;

        foreach (var item in _personaInventories.Inventory)
        {
            if (item.Resource.ResourceSlug == resourceSlug)
                return true;
        }
        return false;
    }

    /// <summary>
    /// 查询背包内是否有指定显示名称的物品（通过DisplayName）
    /// </summary>
    /// <param name="displayName">资源显示名称</param>
    /// <returns>是否拥有该物品</returns>
    public static bool HasItemByDisplayName(string displayName)
    {
        if (string.IsNullOrEmpty(displayName))
        {
            Debug.LogWarning("查询背包物品失败: 资源显示名称不能为空");
            return false;
        }

        if (_personaInventories?.Inventory == null)
            return false;

        foreach (var item in _personaInventories.Inventory)
        {
            if (string.Equals(item.Resource?.DisplayName, displayName, StringComparison.OrdinalIgnoreCase))
                return true;
        }
        return false;
    }

    /// <summary>
    /// 获取背包中指定物品的数量
    /// </summary>
    /// <param name="resourceSlug">资源唯一标识</param>
    /// <returns>物品数量，如果没有则返回0</returns>
    public static uint GetItemQuantity(string resourceSlug)
    {
        if (string.IsNullOrEmpty(resourceSlug))
        {
            Debug.LogWarning("获取背包物品数量失败: 资源标识不能为空");
            return 0;
        }

        if (_personaInventories?.Inventory == null)
            return 0;

        uint quantity = 0;
        foreach (var item in _personaInventories.Inventory)
        {
            if (item.Resource.ResourceSlug == resourceSlug)
                quantity += item.Quantity;
        }
        return quantity;
    }

    #endregion

    public static class EconomyEvents
    {
        public static event Action<uint> OnGOLD_COINChanged;
        public static event System.Action OnInventoryLoaded;

        public static void RaiseGOLD_COINChanged(uint newAmount)
        {
            OnGOLD_COINChanged?.Invoke(newAmount);
        }

        public static void RaiseInventoryLoaded()
        {
            OnInventoryLoaded?.Invoke();
        }
    }

    public static async UniTask AddGOLD_COINAsync(uint num)
    {
        var depositResourcesRequest = new List<Economy.DepositResource>
    {
        new Economy.DepositResource
        {
            ResourceSlug = "GOLD_COIN",
            DepositQuantity = num,
        }
    };

        await DepositResources(depositResourcesRequest);
        await RefreshPersonaInventory();

        uint newAmount = GetItemQuantity("GOLD_COIN");
        EconomyEvents.RaiseGOLD_COINChanged(newAmount);
    }

    public static async void RefreshGOLD_COINUI(TextBlock textBlock)
    {
        uint gold = GetItemQuantity("GOLD_COIN");

        textBlock.Text = gold.ToString();
    }
}