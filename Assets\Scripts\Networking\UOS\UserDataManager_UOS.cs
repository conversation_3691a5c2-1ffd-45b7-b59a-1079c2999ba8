/* ===========
 * 作者: <PERSON><PERSON><PERSON><PERSON>
 * 创建时间: 2025/05/12
 * 非作者最新修改时间：  
 * =========== */
using UnityEngine;
using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using System.IO;
using System.Threading.Tasks;
using Unity.Passport.Runtime;
using Unity.Passport.Runtime.Model;
using Passport;
using System.Linq;
using Unity.UOS.Auth;
using Unity.UOS.Common;
using Unity.UOS.Common.UOSLauncher.Scripts.Auth;

namespace VIC.Networking.UOS
{
    /// <summary>
    /// UOS用户数据结构
    /// </summary>
    [Serializable]
    public struct UserData_UOS
    {
        public string Name;        // 用户名称
        public string UserID;      // UOS用户ID
        public string AvatarUrl;   // 头像URL
        public bool AboveMinAge;   // 是否达到最小年龄要求
        public string PersonaID;   // 当前角色ID
        public string PersonaName; // 当前角色名称
        public string RealmName;   // 当前域名称
        public string RealmID;     // 当前域ID
    }

    /// <summary>
    /// UOS用户数据管理器
    /// 负责管理UOS账号系统的用户数据
    /// </summary>
    public static class UserDataManager_UOS
    {
        #region 常量与字段
        private const string USER_DATA_KEY = "UOS_USER_DATA";

        private static UserData_UOS _currentUserData;
        private static readonly object _userDataLock = new object();

        // 登录状态标识符
        private static bool _isLoggedIn = false;

        // 缓存相关
        private static List<Realm> _cachedRealms = null;
        private static DateTime _lastRealmCacheUpdate = DateTime.MinValue;
        private static readonly TimeSpan _realmCacheExpireTime = TimeSpan.FromMinutes(5); // 域列表缓存5分钟

        // 防止重复获取域列表的锁和标志
        private static bool _isRefreshingRealms = false;
        private static readonly object _realmLock = new object();

        #endregion

        #region 公共属性
        /// <summary> 当前用户数据 </summary>
        public static UserData_UOS CurrentUserData
        {
            get
            {
                lock (_userDataLock)
                {
                    return _currentUserData;
                }
            }
            private set
            {
                lock (_userDataLock)
                {
                    _currentUserData = value;
                }
            }
        }

        /// <summary> 登录状态 </summary>
        public static bool IsLoggedIn => _isLoggedIn;

        #endregion

        #region 事件定义
        /// <summary> 用户数据变更事件 </summary>
        public static event Action<UserData_UOS> OnUserDataChanged;

        /// <summary> 登录状态变更事件 </summary>
        public static event Action<bool> OnLoginStateChanged;

        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化用户数据管理器（加载本地存储数据）
        /// </summary>
        public static void Initialize()
        {
            LoadUserData();
        }

        #endregion

        #region 用户数据更新
        /// <summary>
        /// 从UOS用户信息更新用户数据（头像处理通过CloudSaveManagement）
        /// </summary>
        /// <param name="userInfo">UOS用户信息对象</param>
        /// <returns>异步任务</returns>
        public static async Task UpdateFromUOSUserInfo(UserProfileInfo userInfo)
        {
            if (userInfo == null)
            {
                UnityEngine.Debug.LogError("UOS用户信息为空，无法更新用户数据");
                return;
            }

            // 获取云端头像URL（优先使用云端头像）
            string avatarUrl = userInfo.AvatarUrl;
            try
            {
                var cloudAvatar = await CloudSaveManagement_UOS.GetCurrentUserProfilePictureAsync();
                if (cloudAvatar != null && !string.IsNullOrEmpty(cloudAvatar.DownloadUrl))
                {
                    avatarUrl = cloudAvatar.DownloadUrl;
                }
            }
            catch (Exception ex) when (ex.Message.Contains("未找到User") || ex.Message.Contains("Unauthenticated"))
            {
                UnityEngine.Debug.LogWarning($"获取云端头像时认证异常，使用UOS头像: {ex.Message}");
                // 认证异常时使用UOS默认头像，不影响登录流程
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogWarning($"获取云端头像失败，使用UOS头像: {ex.Message}");
            }

            // 构建新用户数据
            CurrentUserData = new UserData_UOS
            {
                Name = userInfo.Name,
                UserID = userInfo.UserID,
                AvatarUrl = avatarUrl,
                AboveMinAge = userInfo.AboveMinAge ?? false,
                PersonaID = PassportSDK.CurrentPersona?.PersonaID,
                PersonaName = PassportSDK.CurrentPersona?.DisplayName,
                RealmName = CurrentUserData.RealmName,  // 保留原有域信息
                RealmID = CurrentUserData.RealmID        // 保留原有域信息
            };

            // 保存并触发数据变更事件
            SaveUserData();
            OnUserDataChanged?.Invoke(CurrentUserData);
        }

        /// <summary>
        /// 更新当前角色信息（域信息保持不变）
        /// </summary>
        /// <param name="persona">目标角色信息</param>
        public static async Task UpdatePersonaInfo(Persona persona)
        {
            if (persona == null) return;

            UnityEngine.Debug.Log($"开始更新角色信息: {persona.DisplayName}");
            
            // 使用副本避免直接修改原数据
            var userData = CurrentUserData;
            userData.PersonaID = persona.PersonaID;
            userData.PersonaName = persona.DisplayName;

            // 通过CloudSaveManagement_UOS获取角色头像
            try
            {
                string avatarUrl = await CloudSaveManagement_UOS.GetPersonaAvatarUrlAsync(persona.UserID, persona.PersonaID);
                if (!string.IsNullOrEmpty(avatarUrl))
                {
                    userData.AvatarUrl = avatarUrl;
                    UnityEngine.Debug.Log($"成功更新角色头像URL: {avatarUrl}");
                }
                else
                {
                    UnityEngine.Debug.Log("角色没有设置头像，保持原有头像");
                }
            }
            catch (Exception ex) when (ex.Message.Contains("未找到User") || ex.Message.Contains("Unauthenticated"))
            {
                UnityEngine.Debug.LogWarning($"获取角色头像时认证异常，保持原有头像: {ex.Message}");
                // 认证异常时保持原有头像，不影响角色信息更新流程
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogWarning($"获取角色头像失败，保持原有头像: {ex.Message}");
            }

            CurrentUserData = userData;
            SaveUserData();
            OnUserDataChanged?.Invoke(CurrentUserData);
            UnityEngine.Debug.Log($"角色信息更新完成: {persona.DisplayName}");
        }

        /// <summary>
        /// 直接更新用户数据（谨慎使用，建议通过官方接口更新）
        /// </summary>
        /// <param name="userData">完整用户数据对象</param>
        public static void UpdateUserData(UserData_UOS userData)
        {
            CurrentUserData = userData;
            SaveUserData();
            OnUserDataChanged?.Invoke(CurrentUserData);
        }

        /// <summary>
        /// 设置当前域信息（用于切换服务器/域）
        /// </summary>
        /// <param name="realm">目标域对象</param>
        public static void SetRealm(Realm realm)
        {
            lock (_userDataLock)
            {
                _currentUserData.RealmID = realm.RealmID;
                _currentUserData.RealmName = realm.Name;
            }
        }

        #endregion

        #region 数据存储操作
        /// <summary>
        /// 从PlayerPrefs加载用户数据
        /// </summary>
        private static void LoadUserData()
        {
            try
            {
                string json = PlayerPrefs.GetString(USER_DATA_KEY, string.Empty);
                CurrentUserData = string.IsNullOrEmpty(json)
                    ? new UserData_UOS()
                    : JsonConvert.DeserializeObject<UserData_UOS>(json);
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"用户数据解析失败：{ex.Message}");
                CurrentUserData = new UserData_UOS();
            }
        }

        /// <summary>
        /// 将用户数据保存到PlayerPrefs
        /// </summary>
        private static void SaveUserData()
        {
            string json = JsonConvert.SerializeObject(CurrentUserData);
            PlayerPrefs.SetString(USER_DATA_KEY, json);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// 清除本地存储的用户数据（用于登出）
        /// </summary>
        public static void ClearUserData()
        {
            PlayerPrefs.DeleteKey(USER_DATA_KEY);
            PlayerPrefs.Save();
            CurrentUserData = new UserData_UOS();
            OnUserDataChanged?.Invoke(CurrentUserData);
        }
        /// <summary>
        /// 将当前用户数据转换为JSON字符串并保存到文件
        /// </summary>
        /// <returns>JSON文件的保存路径</returns>
        public static string GetCurrentUserDataJson()
        {
            string json = JsonConvert.SerializeObject(CurrentUserData);
            string userDataFile = string.Empty;

            try
            {
                // 在AppData目录下创建一个固定位置保存用户数据
                string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                string vicFolder = Path.Combine(appDataPath, "VIC");
                userDataFile = Path.Combine(vicFolder, "uos_user_data.json");

                // 确保目录存在
                Directory.CreateDirectory(vicFolder);

                // 保存JSON文件
                File.WriteAllText(userDataFile, json);
                // Debug.Log($"UOS用户数据已保存到: {userDataFile}");
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"保存UOS用户数据JSON文件失败: {ex.Message}");
            }

            return userDataFile;
        }

        #endregion

        #region 域与角色管理

        /// <summary>
        /// 获取当前应用下的所有域列表（带缓存）
        /// </summary>
        /// <param name="forceRefresh">是否强制刷新缓存</param>
        /// <returns>域对象列表（异步任务）</returns>
        public static async Task<List<Realm>> GetRealmList(bool forceRefresh = false)
        {
            try
            {
                // 检查缓存是否过期或需要强制刷新
                bool needRefresh = forceRefresh || _cachedRealms == null ||
                    DateTime.Now - _lastRealmCacheUpdate > _realmCacheExpireTime;

                if (needRefresh)
                {
                    // 使用锁防止多个并发刷新
                    lock (_realmLock)
                    {
                        // 双重检查，防止在等待锁的过程中其他线程已经刷新了缓存
                        if (forceRefresh || _cachedRealms == null ||
                            DateTime.Now - _lastRealmCacheUpdate > _realmCacheExpireTime)
                        {
                            if (!_isRefreshingRealms)
                            {
                                _isRefreshingRealms = true;
                                // 在锁外执行异步刷新
                                _ = RefreshRealmCacheAsync();
                            }
                        }
                    }

                    // 等待刷新完成（最多等待10秒）
                    int waitCount = 0;
                    while (_isRefreshingRealms && waitCount < 100)
                    {
                        await Task.Delay(100);
                        waitCount++;
                    }
                }

                return _cachedRealms ?? new List<Realm>();
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"获取域列表失败: {ex.Message}");
                return new List<Realm>();
            }
        }

        /// <summary>
        /// 刷新域列表缓存
        /// </summary>
        private static async Task RefreshRealmCacheAsync()
        {
            try
            {
                UnityEngine.Debug.Log("开始刷新域列表缓存");

                // 直接获取域列表，不需要登录
                var realms = await PassportSDK.Identity.GetRealms();

                // 更新缓存
                _cachedRealms = realms?.Any() == true ? realms : new List<Realm>();
                _lastRealmCacheUpdate = DateTime.Now;

                UnityEngine.Debug.Log($"域列表缓存刷新成功，共 {_cachedRealms.Count} 个域");
            }
            catch (PassportException e)
            {
                UnityEngine.Debug.LogError($"刷新域列表缓存失败：{e.Code}");
                // 如果刷新失败，保持原有缓存或设置为空列表
                if (_cachedRealms == null)
                    _cachedRealms = new List<Realm>();
            }

            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"刷新域列表缓存异常: {ex.Message}");
                if (_cachedRealms == null)
                    _cachedRealms = new List<Realm>();
            }
            finally
            {
                // 无论成功还是失败，都要重置刷新标志
                _isRefreshingRealms = false;
            }
        }

        /// <summary>
        /// 获取指定域下的用户角色
        /// </summary>
        /// <param name="realmID">目标域ID（默认当前域）</param>
        /// <returns>角色对象（异步任务）</returns>
        public static async Task<Persona> GetPersonaByRealm(string realmID = null)
        {
            realmID = string.IsNullOrEmpty(realmID) ? CurrentUserData.RealmID : realmID;

            if (string.IsNullOrEmpty(realmID))
            {
                UnityEngine.Debug.LogError("域ID为空，无法获取角色信息");
                return null;
            }

            try
            {
                return await PassportSDK.Identity.GetPersonaByRealm(realmID);
            }
            catch (PassportException e)
            {
                UnityEngine.Debug.LogError($"角色获取失败：{e.Code}");
                return null;
            }
        }

        /// <summary>
        /// 创建新角色
        /// </summary>
        /// <param name="displayName">角色显示名称</param>
        /// <param name="realmID">目标域ID（默认当前域）</param>
        /// <returns>新建的角色对象（异步任务）</returns>
        public static async Task<Persona> CreatePersona(string displayName, string realmID = null)
        {
            if (string.IsNullOrEmpty(displayName))
            {
                UnityEngine.Debug.LogError("角色名称为空，无法创建角色");
                return null;
            }

            realmID = string.IsNullOrEmpty(realmID) ? CurrentUserData.RealmID : realmID;
            if (string.IsNullOrEmpty(realmID))
            {
                UnityEngine.Debug.LogError("域ID为空，无法创建角色");
                return null;
            }

            try
            {
                return await PassportSDK.Identity.CreatePersona(displayName, realmID);
            }
            catch (PassportException e)
            {
                UnityEngine.Debug.LogError($"角色创建失败：{e.Code}");
                return null;
            }
        }

        /// <summary>
        /// 选择指定角色
        /// </summary>
        /// <param name="persona">目标角色对象</param>
        /// <returns>选择是否成功（异步任务）</returns>
        public static async Task<bool> SelectPersona(Persona persona)
        {
            if (persona == null)
            {
                UnityEngine.Debug.LogError("目标角色为空，选择失败");
                return false;
            }

            try
            {
                UnityEngine.Debug.Log($"开始选择角色: {persona.DisplayName} (ID: {persona.PersonaID})");
                
                // 切换角色时清理背包和头像缓存，因为不同角色有不同的数据
                try
                {
                    TradingSystemManagement_UOS.ClearInventoryCache();
                    CloudSaveManagement_UOS.ClearProfilePictureCache();
                    UnityEngine.Debug.Log("角色切换：已清理背包和头像缓存");
                }
                catch (Exception ex)
                {
                    UnityEngine.Debug.LogWarning($"清理角色相关缓存失败: {ex.Message}");
                }

                await PassportSDK.Identity.SelectPersona(persona.PersonaID);
                await UpdatePersonaInfo(persona);
                UnityEngine.Debug.Log($"角色选择完成: {persona.DisplayName}");
                return true;
            }
            catch (PassportException e)
            {
                UnityEngine.Debug.LogError($"角色选择失败：{e.Code}");
                return false;
            }
        }

        #endregion

        #region 登录状态检查

        /// <summary>
        /// 异步检查UOS系统的登录状态（基于AuthTokenManager）
        /// </summary>
        /// <returns>是否已登录UOS系统</returns>
        public static async Task<bool> CheckUOSLoginStatusAsync()
        {
            try
            {
                var tokenInfo = await AuthTokenManager.GetTokenInfo();
                bool isLoggedIn = tokenInfo != null && !string.IsNullOrEmpty(tokenInfo.AccessToken);
                UnityEngine.Debug.Log($"[UserDataManager_UOS] UOS登录状态检查: {isLoggedIn}");
                return isLoggedIn;
            }
            catch (AuthException ex) when (ex.ErrorCode == Unity.UOS.Common.UOSLauncher.Scripts.Auth.ErrorCode.NeedLogin)
            {
                UnityEngine.Debug.Log("[UserDataManager_UOS] UOS登录状态检查: 需要重新登录");
                return false;
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogWarning($"[UserDataManager_UOS] UOS登录状态检查异常: {ex.Message}");
                return false;
            }
        }
        #endregion

        #region 登录登出处理
        /// <summary>
        /// 处理用户登录流程（自动获取用户信息并更新数据）
        /// </summary>
        /// <returns>异步任务</returns>
        public static async Task HandleLogIn()
        {
            try
            {
                // 登录时先清理所有缓存，确保获取最新数据
                ClearAllCaches();

                var userInfo = await PassportSDK.Identity.GetUserProfileInfo();
                if (userInfo != null)
                {
                    await UpdateFromUOSUserInfo(userInfo);
                    SetLoginState(true);
                    // Debug.Log("用户登录成功，缓存已清理");
                }
                else
                {
                    SetLoginState(false);
                    UnityEngine.Debug.LogError("用户信息获取失败");
                }
            }
            catch (Exception ex)
            {
                SetLoginState(false);
                UnityEngine.Debug.LogError($"登录处理异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 处理用户登出流程（清除本地数据并通知SDK）
        /// </summary>
        public static void HandleLogout()
        {
            ClearUserData();

            // 清理所有相关模块的缓存
            ClearAllCaches();

            if (PassportLoginSDK.Identity.CheckIsLoggedIn())
            {
                PassportLoginSDK.Identity.Logout();
            }
            SetLoginState(false);
            // Debug.Log("用户已登出，所有缓存已清理");
        }

        #endregion

        #region 缓存清理
        /// <summary>
        /// 清理所有相关模块的缓存（用于账号切换）
        /// </summary>
        public static void ClearAllCaches()
        {
            try
            {
                // 清理云存储头像缓存
                CloudSaveManagement_UOS.ClearProfilePictureCache();

            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogWarning($"清理云存储头像缓存失败: {ex.Message}");
            }

            try
            {
                // 清理交易系统缓存
                TradingSystemManagement_UOS.ClearAllCaches();
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogWarning($"清理交易系统缓存失败: {ex.Message}");
            }

            // 清理域列表缓存
            _cachedRealms = null;
            _lastRealmCacheUpdate = DateTime.MinValue;
        }
        #endregion

        #region 私有辅助方法
        /// <summary>
        /// 设置登录状态并触发事件（避免重复调用）
        /// </summary>
        /// <param name="isLoggedIn">新的登录状态</param>
        private static void SetLoginState(bool isLoggedIn)
        {
            if (_isLoggedIn == isLoggedIn) return;
            _isLoggedIn = isLoggedIn;
            OnLoginStateChanged?.Invoke(_isLoggedIn);
        }

        #endregion
    }
}