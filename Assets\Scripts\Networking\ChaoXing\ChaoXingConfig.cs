/* ===========
 * 作者: <PERSON><PERSON><PERSON><PERSON>
 * 创建时间: 2025/01/15
 * 修改时间: 2025/07/04
 * 功能: 超星接口配置管理 - 重新实现以符合API文档要求
 * =========== */
using UnityEngine;

namespace VIC.Networking.ChaoXing
{
    /// <summary>
    /// 超星接口配置，用于管理API密钥、URL等配置信息
    /// 根据超星API文档重新设计
    /// </summary>
    [CreateAssetMenu(fileName = "ChaoXingConfig", menuName = "VIC/ChaoXingConfig", order = 2)]
    public class ChaoXingConfig : ScriptableObject
    {
        [Header("超星接口配置")]
        [SerializeField, Tooltip("超星API接口地址")]
        private string apiUrl = "https://jwdatatb.chaoxing.com/fydatas/fhcyxnfzxy/api/student/vrscore";

        [SerializeField, Tooltip("超星平台分配的密钥 (secret_key)")]
        private string secretKey = "";

        [Header("功能开关")]
        [SerializeField, Tooltip("是否启用成绩提交功能")]
        private bool enableSubmission = true;

        [SerializeField, Tooltip("是否启用调试日志")]
        private bool enableDebugLog = true;

        [SerializeField, Tooltip("是否启用详细的handshake调试信息")]
        private bool enableHandshakeDebug = false;

        [Header("网络配置")]
        [SerializeField, Tooltip("请求超时时间（秒）")]
        private int requestTimeout = 30;

        [SerializeField, Tooltip("失败重试次数")]
        private int retryCount = 3;

        [SerializeField, Tooltip("重试间隔（秒）")]
        private float retryInterval = 2f;
        
        /// <summary>
        /// API接口地址
        /// </summary>
        public string ApiUrl => apiUrl;

        /// <summary>
        /// 密钥 (secret_key)
        /// </summary>
        public string SecretKey => secretKey;

        /// <summary>
        /// 是否启用提交功能
        /// </summary>
        public bool EnableSubmission => enableSubmission;

        /// <summary>
        /// 是否启用调试日志
        /// </summary>
        public bool EnableDebugLog => enableDebugLog;

        /// <summary>
        /// 是否启用详细的handshake调试信息
        /// </summary>
        public bool EnableHandshakeDebug => enableHandshakeDebug;

        /// <summary>
        /// 请求超时时间
        /// </summary>
        public int RequestTimeout => requestTimeout;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount => retryCount;

        /// <summary>
        /// 重试间隔
        /// </summary>
        public float RetryInterval => retryInterval;
        
        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        /// <returns>配置是否有效</returns>
        public bool IsValid()
        {
            if (string.IsNullOrEmpty(apiUrl))
            {
                Debug.LogError("[ChaoXingConfig] API URL未配置");
                return false;
            }
            
            if (string.IsNullOrEmpty(secretKey))
            {
                Debug.LogError("[ChaoXingConfig] 密钥未配置");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 运行时设置密钥
        /// </summary>
        /// <param name="key">密钥</param>
        public void SetSecretKey(string key)
        {
            secretKey = key;
        }
        
        /// <summary>
        /// 运行时设置API URL
        /// </summary>
        /// <param name="url">API URL</param>
        public void SetApiUrl(string url)
        {
            apiUrl = url;
        }
        
        /// <summary>
        /// 运行时设置提交开关
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void SetSubmissionEnabled(bool enabled)
        {
            enableSubmission = enabled;
        }
    }
}