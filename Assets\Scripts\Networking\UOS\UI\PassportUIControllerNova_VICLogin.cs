/* ===========
 * 作者: <PERSON><PERSON><PERSON><PERSON>
 * 创建时间: 2025/05/12
 * 非作者最新修改时间：  
 * =========== */
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using TMPro;
using System;
using System.Linq;
using Unity.Passport.Runtime.Model;
using System.Threading.Tasks;
using UnityEngine.Networking;
using System.Text.RegularExpressions;
using Passport;
using Nova;
using NovaSamples.UIControls;
using VIC.UI;
using VIC.Networking.ChaoXing;
using Unity.Passport.Runtime.UI;
using Unity.Passport.Runtime;
using NaughtyAttributes;
using VIC.Networking.VICLogin.UI;
using VIC.Utils;
using Unity.UOS.Auth;

namespace VIC.Networking.UOS.UI
{
    public class PassportUIControllerNova_VICLogin : PassportUIBase_NOVA
    {
        [Header("主面板")]
        [SerializeField, Label("协议面板")] private UIBlock2D tosPanel;
        [SerializeField, Label("加载中")] private UIBlock2D loadingPanel;
        [SerializeField, Label("登录界面")] private VICLoginUI vICLoginLoginUI;

        private PassportTos _tos = new();
        private UserStatus _status = 0;
        private List<UserLoginInfo> _userList = new List<UserLoginInfo>();
        private UserStatus CurrentUserStatus => _status;
        private LoginType _currentLoginType = LoginType.None;
        
        // 防止重复调用的状态标志
        private bool _isInitialized = false;
        private bool _hasCheckedUserStatus = false;

        /// <summary>
        /// 初始化
        /// </summary>
        public override async Task Init()
        {
            // 防止重复初始化
            if (_isInitialized)
            {
                Debug.Log("PassportUIControllerNova 已经初始化，跳过重复初始化");
                return;
            }
            
            try
            {
                // 关闭所有面板
                CloseAllPanels();

                Loading(true);
                // 调用 SDK
                var userInfo = await PassportLoginSDK.Init(true);
                if (userInfo != null)
                {
                    UIMessage.Show($"{userInfo.Name} 登录中...");
                    // 手动调用 Login，以显示提示消息
                    await PassportLoginSDK.Identity.Login(userInfo);
                }

                Loading(false);
                _isInitialized = true;
            }
            catch (PassportException e)
            {
                UOSExceptionHandler.HandlePassportException(e, "Passport初始化");
                HandleLoginError(e, LoginType.LoginByUserInfo);
                Loading(false);
            }
            catch (Exception ex)
            {
                UOSExceptionHandler.HandleGeneralException(ex, "Passport初始化");
                Loading(false);
            }

            // 根据 app 在 uos 上的配置，更新 UI 显示：

            if (_currentLoginType == LoginType.None)
            {
                // 初始化用户状态并展示对应的面板
                await InitUserStatus(); // 取消注释这一行
            }
        }
        /// <summary>
        /// 关闭所有面板
        /// </summary>
        private void CloseAllPanels()
        {
            if (tosPanel != null && tosPanel.gameObject != null)
                tosPanel.gameObject.SetActive(false);
                
            if (vICLoginLoginUI != null && vICLoginLoginUI.gameObject != null)
                vICLoginLoginUI.gameObject.SetActive(false);
        }

        public override void Login()
        {
            ShowPanel();
        }

        private async void OnLoginComplete(LoginResult result)
        {
            // 登录失败
            if (result.ErrorMessage != null)
            {
                UIMessage.Show(result.ErrorMessage.Message, MessageType.Error);
                UpdateStatus(UserStatus.LoggedIn, false);
                return;
            }

            // 登录成功时清理所有缓存，确保获取最新数据
            try
            {
                UserDataManager_UOS.ClearAllCaches();
                Debug.Log("登录成功：已清理所有缓存");
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"清理缓存失败: {ex.Message}");
            }

            UIMessage.Show("登录成功");
            OnCallback(PassportEvent_NOVA.LoggedIn);

            // 只有在还没有检查过用户状态时才调用
            if (!_hasCheckedUserStatus)
            {
                await CheckUserStatusAfterLoggedIn();
            }
            ShowPanel();
        }

        /// <summary>
        /// 拒绝用户协议
        /// </summary>
        public void RejectTos()
        {
            tosPanel.gameObject.SetActive(false);
            OnCallback(PassportEvent_NOVA.RejectedTos);
        }

        /// <summary>
        /// 展示协议面板
        /// </summary>
        private async void ShowTosPanel()
        {
            var tosPanelController = tosPanel.GetComponent<TOSPanelNOVA>();
            tosPanelController.Loading(true);
            // 加载中
            Loading(true);

            // 获取协议信息
            try
            {
                _tos = await PassportLoginSDK.Identity.GetTos();
                tosPanelController.Init(_tos);

                // 如果协议信息为空，说明用户没有配置，则跳过该步骤
                if (_tos == null)
                {
                    Loading(false);
                    UpdateStatus(UserStatus.Tos, true);
                    ShowPanel();
                    return;
                }

                tosPanelController.Loading(false);
                Loading(false);
            }
            catch (PassportException e)
            {
                UIMessage.Show(e);
                tosPanelController.Loading(false);
            }
        }

        /// <summary>
        /// 同意协议
        /// </summary>
        /// <returns></returns>
        public async void AcceptTos()
        {
            // 点击同意协议按钮
            try
            {
                await PassportLoginSDK.Identity.AcceptTos(_tos);
                // 成功
                UpdateStatus(UserStatus.Tos, true);
                if (_currentLoginType == LoginType.LoginByUserInfo)
                {
                    var loginResult = await PassportLoginSDK.Identity.Login();
                    OnLoginComplete(loginResult);
                }
                else
                {
                    ShowPanel();
                }

            }
            catch (PassportException e)
            {
                // 失败
                UpdateStatus(UserStatus.Tos, false);
                UIMessage.Show(e);
            }
        }

        /// <summary>
        /// 根据状态码决定展示的面板
        /// </summary>
        private void ShowPanel()
        {
            // 先关闭所有面板
            CloseAllPanels();
            Loading(false);

            // 检查各组件是否存在
            if (!ValidateUIComponents()) return;

            if ((CurrentUserStatus & UserStatus.Tos) != UserStatus.Tos)
            {
                ShowTosPanel();
            }
            else
            {
                // TOS已完成，检查VICLogin登录状态
                if (!VIC.Networking.VICLogin.VICLoginStorageHandler.HasStoredLoginInfo)
                {
                    // 显示VICLogin登录界面
                    ShowVICLoginLoginPanel();
                }
                else
                {
                    // VICLogin已登录，完成所有环节
                    OnCallback(PassportEvent_NOVA.Completed);
                }
            }
        }

        /// <summary>
        /// 显示VICLogin登录面板
        /// </summary>
        private void ShowVICLoginLoginPanel()
        {
            if (vICLoginLoginUI != null && vICLoginLoginUI.gameObject != null)
            {
                vICLoginLoginUI.gameObject.SetActive(true);
            }
            else
            {
                Debug.LogError("VICLogin登录界面未找到，请确保在Inspector中正确设置vICLoginLoginUI引用");
                // 如果找不到VICLogin登录界面，直接触发完成事件
                OnCallback(PassportEvent_NOVA.Completed);
            }
        }

        /// <summary>
        /// 验证UI组件是否存在
        /// </summary>
        /// <returns>是否所有必要组件都存在</returns>
        private bool ValidateUIComponents()
        {
            bool isValid = true;

            if (tosPanel == null || tosPanel.gameObject == null)
            {
                Debug.LogError("协议面板未找到");
                isValid = false;
            }
            
            if (vICLoginLoginUI == null || vICLoginLoginUI.gameObject == null)
            {
                Debug.LogWarning("VICLogin登录界面未找到，请确保在Inspector中正确设置vICLoginLoginUI引用");
            }
            
            return isValid;
        }
        private void UpdateStatus(UserStatus subStatus, bool additive)
        {
            // additive：叠加指定位置的完成态
            if (additive)
            {
                _status |= subStatus;
            }
            else
            {
                _status &= ~subStatus;
            }
        }

        public override void Logout()
        {
            // 使用统一的缓存清理
            UOSExceptionHandler.ClearCaches("Passport登出", true);

            PassportLoginSDK.Identity.Logout();

            // 重置状态标志，允许下次重新初始化
            _isInitialized = false;
            _hasCheckedUserStatus = false;

            // 修改状态
            UpdateStatus(UserStatus.ReLogin, true);
            UpdateStatus(UserStatus.LoggedIn, false);

            ShowPanel();
            OnCallback(PassportEvent_NOVA.LoggedOut);
        }

        /// <summary>
        /// 处理登录错误
        /// </summary>
        private void HandleLoginError(PassportException e, LoginType loginType)
        {
            switch (e.Code)
            {
                case ErrorCode.FailedPreconditionPassportNeedAcceptTos:
                    _currentLoginType = loginType;
                    UpdateStatus(UserStatus.Tos, false);
                    break;
                case ErrorCode.UnauthenticatedPassportRefreshTokenExpired:
                    _currentLoginType = LoginType.None; // 需要使用新账号重新登录
                    UpdateStatus(UserStatus.ReLogin, false);
                    break;
            }
        }

        /// <summary>
        /// 处理外部登录成功（如VICLogin登录、超星登录）
        /// 直接完成认证流程，避免重复的协议检查
        /// </summary>
        public async void OnExternalLoginSuccess(string loginSource = "外部登录")
        {
            // 使用统一的缓存清理
            // VIC.Utils.CacheManager.ClearAllCaches($"{loginSource}成功");

            // 如果是VICLogin登录，更新UOS用户数据并进行UOS基础认证
            if (loginSource.Contains("VICLogin登录") && VIC.Networking.VICLogin.VICLoginStorageHandler.HasStoredLoginInfo)
            {
                try
                {
                    var currentUserData = UserDataManager_UOS.CurrentUserData;
                    currentUserData.Name = VIC.Networking.VICLogin.VICLoginStorageHandler.StoredNickname;
                    currentUserData.UserID = VIC.Networking.VICLogin.VICLoginStorageHandler.StoredUsername;
                    UserDataManager_UOS.UpdateUserData(currentUserData);
                    Debug.Log($"已更新UOS用户数据：Name={currentUserData.Name}, UserID={currentUserData.UserID}");

                    // 进行UOS基础用户认证（包含角色信息）
                    string userId = VIC.Networking.VICLogin.VICLoginStorageHandler.GetStoredUserId();
                    string personaId = VIC.Networking.VICLogin.VICLoginStorageHandler.GetStoredPersonaId();
                    string personaDisplayName = VIC.Networking.VICLogin.VICLoginStorageHandler.GetStoredPersonaName();

                    Debug.Log($"[PassportUIControllerNova_VICLogin] VICLogin成功后进行UOS基础认证 - UserId: {userId}, PersonaId: {personaId}, PersonaDisplayName: {personaDisplayName}");
                    await AuthTokenManager.ExternalLogin(userId, personaId, personaDisplayName);
                    Debug.Log("[PassportUIControllerNova_VICLogin] UOS基础认证成功");

                    Debug.Log(PassportLoginSDK.Identity.CheckIsLoggedIn());
                }
                catch (Exception ex)
                {
                    Debug.LogError($"VICLogin成功后UOS认证失败: {ex.Message}");
                    UIMessage.Show("UOS认证失败，请重试", MessageType.Error);
                    return;
                }
            }
            // 如果是超星登录，更新UOS用户数据但不进行基础认证
            // 基础认证将在DemoUIControllerNOVA.TryExternalLogin()中统一处理
            else if (loginSource.Contains("ChaoXingLogin") && UserDataManager_ChaoXing.HasValidLoginData())
            {
                try
                {
                    var chaoXingLoginData = UserDataManager_ChaoXing.GetLoginData();
                    var currentUserData = UserDataManager_UOS.CurrentUserData;

                    // 使用超星的userId作为用户名，resourceId作为显示名称
                    currentUserData.Name = chaoXingLoginData.userId; // 使用resourceId作为显示名称
                    currentUserData.UserID = chaoXingLoginData.userId;   // 使用userId作为用户ID
                    UserDataManager_UOS.UpdateUserData(currentUserData);
                    Debug.Log($"[PassportUIControllerNova_VICLogin] 已更新UOS用户数据：Name={currentUserData.Name}, UserID={currentUserData.UserID}");

                    Debug.Log("[PassportUIControllerNova_VICLogin] 超星登录信息已准备，等待域特定认证");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[PassportUIControllerNova_VICLogin] 超星登录数据处理失败: {ex.Message}");
                    UIMessage.Show("超星登录数据处理失败，请重试", MessageType.Error);
                    return;
                }
            }

            UIMessage.Show($"{loginSource}成功");

            // 直接触发完成事件，避免重复的协议检查
            OnCallback(PassportEvent_NOVA.Completed);
        }

        /// <summary>
        /// 检查用户登录后的状态
        /// </summary>
        private async Task CheckUserStatusAfterLoggedIn()
        {
            // 防止重复检查用户状态
            if (_hasCheckedUserStatus)
            {
                Debug.Log("用户状态已经检查过，跳过重复检查");
                return;
            }
            
            UpdateStatus(UserStatus.LoggedIn, true);
            UpdateStatus(UserStatus.ReLogin, false);

            // 登录成功后，检查协议同意状态
            try
            {
                // 检查是否同意最新协议
                var needAcceptTos = await PassportLoginSDK.Identity.CheckUserNeedAcceptTos();
                UpdateStatus(UserStatus.Tos, !needAcceptTos);
                _hasCheckedUserStatus = true;
            }
            catch (PassportException e)
            {
                UIMessage.Show(e);
            }
        }

        /// <summary>
        /// 初始化用户状态
        /// </summary>
        private async Task InitUserStatus()
        {
            try
            {
                // 检查是否同意最新协议
                var needAcceptTos = await PassportLoginSDK.Identity.CheckUserNeedAcceptTos();
                UpdateStatus(UserStatus.Tos, !needAcceptTos);
            }
            catch (PassportException e)
            {
                UIMessage.Show(e);
            }

            try
            {
                // 检查登录状态
                var loggedIn = PassportLoginSDK.Identity.CheckIsLoggedIn();
                UpdateStatus(UserStatus.LoggedIn, loggedIn);

                if (loggedIn)
                {
                    var userInfo = await PassportSDK.Identity.GetUserProfileInfo();
                    var comma = string.IsNullOrEmpty(userInfo.Name) ? "" : "，";
                    UIMessage.Show($"{userInfo.Name}{comma}欢迎进入游戏!", MessageType.Info, 3f);
                    OnCallback(PassportEvent_NOVA.LoggedIn);
                    await CheckUserStatusAfterLoggedIn();
                }
                else
                {
                    if (_userList.Count > 0)
                    {
                        UpdateStatus(UserStatus.ReLogin, true);
                    }
                }
            }
            catch (PassportException e)
            {
                UIMessage.Show(e);
            }
        }

        /// <summary>
        /// 显示加载中
        /// </summary>
        private void Loading(bool isLoading)
        {
            if (loadingPanel != null)
            {
                loadingPanel.gameObject.SetActive(isLoading);
            }
        }

        #region Cancel/Return Button Logic

        /// <summary>
        /// 关闭TOS面板，根据当前登录类型决定返回逻辑
        /// </summary>
        public void CancelTos()
        {
            tosPanel.gameObject.SetActive(false);

            // 如果是重新登录过程中的TOS，返回重新登录面板
            if (_currentLoginType == LoginType.ReLogin)
            {
                UpdateStatus(UserStatus.Tos, false);
                UpdateStatus(UserStatus.ReLogin, true);
            }
            else
            {
                // 其他情况返回主登录面板
                UpdateStatus(UserStatus.Tos, false);
            }

            ShowPanel();
        }

        /// <summary>
        /// 统一的返回按钮处理逻辑
        /// </summary>
        public void HandleBackButton()
        {
            if (tosPanel.gameObject.activeInHierarchy)
            {
                CancelTos();
            }
            else
            {
                // 默认关闭所有面板
                CloseAllPanels();
            }
        }

        #endregion
    }
}