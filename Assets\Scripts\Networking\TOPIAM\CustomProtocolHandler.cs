/*===========
* 作者: Zhu<PERSON><PERSON>ong
* 创建时间: 2025/06/12
* 非作者最新修改时间：
===========*/
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine;
using System.Web;  // 添加缺失的命名空间引用
using Unity.UOS.Auth; // UOS外部登录支持
using System.Threading.Tasks;
using System.Reflection; // 添加反射支持

namespace VIC.Networking.TOPIAM
{
    /// <summary>
    /// 自定义协议处理器，用于处理vic://协议启动的授权码登录
    /// 此类已弃用，请使用新的协议处理系统
    /// </summary>
    [System.Obsolete("此类已弃用，请使用新的协议处理系统", false)]
    public static class CustomProtocolHandler
    {
        public static event Action<bool, string> OnAuthorizationCodeProcessed;
        private static bool isInitialized = false;
        
        // 存储外部登录信息
        public static string StoredUserId { get; private set; }
        public static string StoredPersonaId { get; private set; }
        
        /// <summary>
        /// 初始化协议处理器，检查启动参数中的协议URL并启动IPC监听
        /// </summary>
        public static void Initialize()
        {
            if (isInitialized)
            {
                Debug.Log("[CustomProtocolHandler] 已经初始化过了");
                return;
            }
            
            isInitialized = true;
            Debug.Log("[CustomProtocolHandler] 开始初始化协议处理器");
            
            // 检查命令行参数中是否有协议URL
            CheckCommandLineForProtocolUrl();
            
            // 启动IPC客户端监听
            InitializeIPCClient();
        }
        
        /// <summary>
        /// 初始化IPC客户端
        /// </summary>
        private static void InitializeIPCClient()
        {
            try
            {
                // 确保IPC客户端实例存在
                var ipcClient = OAuthIPCClient.Instance;
                
                // 订阅IPC事件
                OAuthIPCClient.OnAuthDataReceived += OnIPCAuthDataReceived;
                OAuthIPCClient.OnIPCError += OnIPCError;
                
                Debug.Log("[CustomProtocolHandler] IPC客户端初始化成功");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CustomProtocolHandler] IPC客户端初始化失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 处理通过IPC接收到的授权数据
        /// </summary>
        /// <param name="authData">授权数据</param>
        private static void OnIPCAuthDataReceived(AuthCallbackData authData)
        {
            Debug.Log($"[CustomProtocolHandler] 通过IPC接收到授权数据: {authData.code?.Substring(0, Math.Min(10, authData.code?.Length ?? 0))}...");
            
            if (!string.IsNullOrEmpty(authData.code))
            {
                ProcessAuthorizationCode(authData.code, authData.state);
            }
            else
            {
                Debug.LogError("[CustomProtocolHandler] IPC接收到的授权码为空");
                OnAuthorizationCodeProcessed?.Invoke(false, "IPC接收到的授权码为空");
            }
        }
        
        /// <summary>
        /// 处理IPC错误
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        private static void OnIPCError(string errorMessage)
        {
            Debug.LogWarning($"[CustomProtocolHandler] IPC错误: {errorMessage}");
            // 这里可以根据需要处理IPC错误，比如回退到其他方式
        }
        
        /// <summary>
        /// 检查命令行参数中的协议URL
        /// </summary>
        private static void CheckCommandLineForProtocolUrl()
        {
            string[] args = System.Environment.GetCommandLineArgs();
            
            foreach (string arg in args)
            {
                if (arg.StartsWith("vic://", StringComparison.OrdinalIgnoreCase))
                {
                    Debug.Log($"[CustomProtocolHandler] 检测到协议URL: {arg}");
                    HandleProtocolUrl(arg);
                    return;
                }
            }
            
            Debug.Log("[CustomProtocolHandler] 未检测到协议URL参数");
        }
        
        /// <summary>
        /// 解析vic://协议URL并提取授权码
        /// </summary>
        /// <param name="protocolUrl">完整的vic://协议URL</param>
        /// <returns>解析结果，包含code、state等参数</returns>
        public static ProtocolUrlData ParseProtocolUrl(string protocolUrl)
        {
            if (string.IsNullOrEmpty(protocolUrl))
            {
                Debug.LogError("[CustomProtocolHandler] 协议URL为空");
                return null;
            }

            Debug.Log($"[CustomProtocolHandler] 解析协议URL: {protocolUrl}");

            try
            {
                // 检查是否是vic://协议
                if (!protocolUrl.StartsWith("vic://", StringComparison.OrdinalIgnoreCase))
                {
                    Debug.LogError("[CustomProtocolHandler] 不是有效的vic://协议URL");
                    return null;
                }

                // 解析URL参数
                var uri = new Uri(protocolUrl);
                var query = uri.Query;
                
                // 移除开头的问号
                if (query.StartsWith("?"))
                {
                    query = query.Substring(1);
                }
                
                // 使用WWWForm的解析方法
                Dictionary<string, string> queryParams = new Dictionary<string, string>();
                foreach (string param in query.Split('&'))
                {
                    string[] parts = param.Split('=');
                    if (parts.Length == 2)
                    {
                        string key = WWW.UnEscapeURL(parts[0]);
                        string value = WWW.UnEscapeURL(parts[1]);
                        queryParams[key] = value;
                    }
                }

                var result = new ProtocolUrlData
                {
                    Host = uri.Host,
                    Code = queryParams["code"],
                    State = queryParams["state"],
                    Timestamp = queryParams["timestamp"]
                };

                Debug.Log($"[CustomProtocolHandler] 解析结果 - Host: {result.Host}, Code: {result.Code?.Substring(0, Math.Min(10, result.Code.Length))}..., State: {result.State}");

                return result;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CustomProtocolHandler] 解析协议URL失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 处理授权码登录
        /// </summary>
        /// <param name="code">授权码</param>
        /// <param name="state">状态参数</param>
        public static void ProcessAuthorizationCode(string code, string state = null)
        {
            if (string.IsNullOrEmpty(code))
            {
                Debug.LogError("[CustomProtocolHandler] 授权码为空");
                OnAuthorizationCodeProcessed?.Invoke(false, "授权码为空");
                return;
            }

            Debug.Log($"[CustomProtocolHandler] 开始处理授权码登录，Code: {code.Substring(0, Math.Min(10, code.Length))}...");

            // 使用现有的OAuth2CodeExchange来交换token
            OAuth2CodeExchange.ExchangeCodeForToken(code, (success, tokenResponse) =>
            {
                if (success && tokenResponse != null)
                {
                    Debug.Log("[CustomProtocolHandler] 授权码交换成功");
                    
                    // 保存token信息
                    OAuth2Service.SaveTokenExtras(tokenResponse);
                    
                    // 设置登录状态，让AuthenticationManager统一处理用户信息获取
                    AuthenticationManager.isLoggedIn = true;
                    AuthenticationManager.TriggerLoginStateChanged(true);
                    
                    // 获取用户信息后进行UOS外部登录
                    PerformUOSExternalLogin();
                    
                    OnAuthorizationCodeProcessed?.Invoke(true, "登录成功");
                    Debug.Log("[CustomProtocolHandler] 用户登录成功");
                }
                else
                {
                    Debug.LogError("[CustomProtocolHandler] 授权码交换失败");
                    OnAuthorizationCodeProcessed?.Invoke(false, "授权码交换失败");
                }
            });
        }

        /// <summary>
        /// 执行UOS外部用户登录
        /// 使用TOPIAM的preferred_username作为userId进行登录
        /// </summary>
        public static async void PerformUOSExternalLogin()
        {
            try
            {
                Debug.Log("[CustomProtocolHandler] 开始UOS外部用户登录");
                
                // 获取TOPIAM用户信息
                UserDataService.GetUserData(async userData =>
                {
                    if (userData != null && userData.TryGetValue("preferred_username", out string userId))
                    {
                        if (!string.IsNullOrEmpty(userId))
                        {
                            // 获取用户显示名称，优先使用name，其次使用preferred_username
                            string personaDisplayName = userData.TryGetValue("name", out string name) && !string.IsNullOrEmpty(name) ? name : userId;
                            string personaId = userId; // 使用userId作为personaId
                            
                            Debug.Log($"[CustomProtocolHandler] 存储外部登录信息 - UserId: {userId}, PersonaId: {personaId}, PersonaDisplayName: {personaDisplayName}");
                            
                            // 存储外部登录信息
                            StoredUserId = userId;
                            StoredPersonaId = personaId;
                            
                            Debug.Log($"[CustomProtocolHandler] 存储的登录信息 - UserId: {StoredUserId}, PersonaId: {StoredPersonaId}");
                            
                            // 直接触发Completed事件，不在这里调用ExternalLogin
                            // ExternalLogin将在DemoUIControllerNOVA的TryExternalLogin方法中调用
                            CallPassportUIOnLoginComplete(true, "外部登录信息已存储，等待选择服务器后完成登录");
                        }
                        else
                        {
                            Debug.LogError("[CustomProtocolHandler] preferred_username为空，无法进行UOS外部登录");
                            CallPassportUIOnLoginComplete(false, "用户ID为空");
                        }
                    }
                    else
                    {
                        Debug.LogError("[CustomProtocolHandler] 无法获取用户信息或preferred_username字段");
                        CallPassportUIOnLoginComplete(false, "无法获取用户信息");
                    }
                }, true); // 强制刷新用户数据
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CustomProtocolHandler] UOS外部登录过程中发生异常: {ex.Message}");
                CallPassportUIOnLoginComplete(false, $"登录异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发PassportEvent_NOVA.Completed事件
        /// </summary>
        /// <param name="success">是否成功</param>
        /// <param name="message">消息</param>
        private static void CallPassportUIOnLoginComplete(bool success, string message)
        {
            try
            {
                // 查找PassportUIControllerNova实例
                var passportController = UnityEngine.Object.FindObjectOfType<VIC.Networking.UOS.UI.PassportUIControllerNova_VICLogin>();
                if (passportController != null)
                {
                    if (success)
                    {
                        // 登录成功，触发Completed事件
                        passportController.OnCallback?.Invoke(VIC.Networking.UOS.UI.PassportEvent_NOVA.Completed);
                        Debug.Log("[CustomProtocolHandler] 已触发PassportEvent_NOVA.Completed事件");
                    }
                    else
                    {
                        // 登录失败，可以考虑触发其他事件或显示错误信息
                        Debug.LogError($"[CustomProtocolHandler] 登录失败: {message}");
                        // 这里可以根据需要触发其他事件或处理失败情况
                    }
                }
                else
                {
                    Debug.LogError("[CustomProtocolHandler] 未找到PassportUIControllerNova实例");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CustomProtocolHandler] 触发PassportEvent失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理完整的vic://协议URL
        /// </summary>
        /// <param name="protocolUrl">完整的协议URL</param>
        public static void HandleProtocolUrl(string protocolUrl)
        {
            var urlData = ParseProtocolUrl(protocolUrl);
            if (urlData != null && !string.IsNullOrEmpty(urlData.Code))
            {
                ProcessAuthorizationCode(urlData.Code, urlData.State);
            }
            else
            {
                Debug.LogError("[CustomProtocolHandler] 无法从协议URL中提取有效的授权码");
                OnAuthorizationCodeProcessed?.Invoke(false, "无效的协议URL或缺少授权码");
            }
        }

        /// <summary>
        /// 获取授权URL用于外部浏览器登录
        /// </summary>
        /// <returns>授权URL</returns>
        // public static string GetAuthorizationUrl()
        // {
        //     string authEndpoint = OAuth2DiscoveryService.GetAuthorizationEndpoint();
        //     if (string.IsNullOrEmpty(authEndpoint))
        //     {
        //         Debug.LogError("[CustomProtocolHandler] 授权端点未初始化");
        //         return string.Empty;
        //     }

        //     string clientId = AuthConfigUtil.GetClientId();
        //     string redirectUri = AuthConfigUtil.GetRedirectUri();
        //     string state = GenerateRandomState();
            
        //     // 保存state用于验证
        //     PlayerPrefs.SetString("oauth2_state", state);
        //     PlayerPrefs.Save();

        //     string authUrl = $"{authEndpoint}?client_id={Uri.EscapeDataString(clientId)}&response_type=code&redirect_uri={Uri.EscapeDataString(redirectUri)}&scope=openid%20profile&state={Uri.EscapeDataString(state)}";
            
        //     Debug.Log($"[CustomProtocolHandler] 生成授权URL: {authUrl}");
        //     return authUrl;
        // }

        /// <summary>
        /// 验证state参数
        /// </summary>
        /// <param name="receivedState">接收到的state</param>
        /// <returns>是否有效</returns>
        public static bool ValidateState(string receivedState)
        {
            string savedState = PlayerPrefs.GetString("oauth2_state", string.Empty);
            bool isValid = !string.IsNullOrEmpty(savedState) && savedState == receivedState;
            
            if (isValid)
            {
                // 清除已使用的state
                PlayerPrefs.DeleteKey("oauth2_state");
                PlayerPrefs.Save();
            }
            
            return isValid;
        }

        /// <summary>
        /// 生成随机state字符串
        /// </summary>
        /// <returns>随机state</returns>
        private static string GenerateRandomState()
        {
            const string chars = "abcdefghijklmnopqrstuvwxyz0123456789";
            var random = new System.Random();
            var result = new char[32];
            
            for (int i = 0; i < result.Length; i++)
            {
                result[i] = chars[random.Next(chars.Length)];
            }
            
            return new string(result);
        }
    }

    /// <summary>
    /// 协议URL解析结果数据结构
    /// </summary>
    [Serializable]
    public class ProtocolUrlData
    {
        public string Host;
        public string Code;
        public string State;
        public string Timestamp;
    }
}