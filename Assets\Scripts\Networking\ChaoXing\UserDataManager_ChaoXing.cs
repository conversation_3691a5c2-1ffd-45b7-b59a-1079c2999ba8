using System;
using UnityEngine;
using Newtonsoft.Json;

namespace VIC.Networking.ChaoXing
{
    /// <summary>
    /// 超星用户数据管理器 - 统一管理所有超星相关数据
    /// </summary>
    public static class UserDataManager_ChaoXing
    {
        #region 事件定义
        /// <summary>
        /// 登录状态变化事件
        /// </summary>
        public static event System.Action<bool, string> OnLoginStatusChanged;
        #endregion

        #region 用户数据字段
        private static ChaoXingLoginData _cachedLoginData;
        private static string _cachedState = string.Empty;
        private static string _storedUOSId = string.Empty;
        #endregion

        #region 公共属性
        /// <summary>
        /// 当前用户ID
        /// </summary>
        public static string CurrentUserId => _cachedLoginData?.userId;
        
        /// <summary>
        /// 当前资源ID
        /// </summary>
        public static string CurrentResourceId => _cachedLoginData?.resourceId;
        
        /// <summary>
        /// 获取当前登录数据
        /// </summary>
        public static ChaoXingLoginData GetLoginData() => _cachedLoginData;
        
        /// <summary>
        /// 检查是否有有效登录数据
        /// </summary>
        public static bool HasValidLoginData() => _cachedLoginData?.HasValidData() ?? false;

        /// <summary>
        /// 存储的UOS ID
        /// </summary>
        public static string StoredUOSId
        {
            get => _storedUOSId;
            set
            {
                _storedUOSId = value;
                Debug.Log($"[UserDataManager_ChaoXing] UOS ID已更新: {value}");
            }
        }
        #endregion

        #region 数据管理方法
        /// <summary>
        /// 设置登录数据
        /// </summary>
        public static void SetLoginData(ChaoXingLoginData loginData)
        {
            if (loginData == null)
            {
                Debug.LogError("[UserDataManager_ChaoXing] 登录数据为空");
                return;
            }

            _cachedLoginData = loginData;
            Debug.Log($"[UserDataManager_ChaoXing] 登录数据已更新 - userId: {loginData.userId}");
            
            // 触发登录成功事件
            OnLoginStatusChanged?.Invoke(true, "登录数据已更新");
        }

        /// <summary>
        /// 清除登录数据
        /// </summary>
        public static void ClearLoginData()
        {
            _cachedLoginData = null;
            _cachedState = string.Empty;
            Debug.Log("[UserDataManager_ChaoXing] 登录数据已清除");

            OnLoginStatusChanged?.Invoke(false, "登录数据已清除");
        }

        /// <summary>
        /// 清除缓存的登录数据（CacheManager调用的方法）
        /// </summary>
        public static void ClearCachedLoginData()
        {
            ClearLoginData();
            _storedUOSId = string.Empty;
            Debug.Log("[UserDataManager_ChaoXing] 所有缓存数据已清除");
        }
        #endregion

        #region State管理
        /// <summary>
        /// 生成并保存state
        /// </summary>
        public static string GenerateAndSaveState()
        {
            _cachedState = GenerateRandomState();
            return _cachedState;
        }

        /// <summary>
        /// 验证state
        /// </summary>
        public static bool ValidateState(string receivedState)
        {
            bool isValid = !string.IsNullOrEmpty(_cachedState) && _cachedState == receivedState;
            if (isValid)
            {
                _cachedState = string.Empty; // 清除已使用的state
            }
            return isValid;
        }

        /// <summary>
        /// 获取当前state
        /// </summary>
        public static string GetCachedState() => _cachedState;

        /// <summary>
        /// 清除state
        /// </summary>
        public static void ClearCachedState()
        {
            _cachedState = string.Empty;
        }

        private static string GenerateRandomState()
        {
            const string chars = "abcdefghijklmnopqrstuvwxyz0123456789";
            var random = new System.Random();
            var result = new char[32];
            for (int i = 0; i < result.Length; i++)
            {
                result[i] = chars[random.Next(chars.Length)];
            }
            return new string(result);
        }

        internal static void Initialize()
        {
            CustomProtocolHandler.CheckCommandLineArgs();
        }
        #endregion
    }

    /// <summary>
    /// 超星登录数据结构
    /// </summary>
    [Serializable]
    public class ChaoXingLoginData
    {
        public string fid;
        public string confSubjectId;
        public string classId;
        public string userId;
        public string usertype;
        public string resourceId;
        public string handshake;
        public string mode;
        public string timestamp;
        public string source;

        /// <summary>
        /// 检查数据有效性
        /// </summary>
        public bool HasValidData()
        {
            return !string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(resourceId);
        }
    }
}