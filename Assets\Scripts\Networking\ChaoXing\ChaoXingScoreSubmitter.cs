/* ===========
 * 作者: Zhu<PERSON>Rong
 * 创建时间: 2025/01/15
 * 修改时间: 2025/07/04
 * 功能: 向超星接口提交成绩数据 - 重新实现以符合API文档要求
 * =========== */
using System;
using System.Collections;
using System.Security.Cryptography;
using System.Text;
using UnityEngine;
using UnityEngine.Networking;
using Newtonsoft.Json;

namespace VIC.Networking.ChaoXing
{
    /// <summary>
    /// 超星成绩提交器 - 负责向超星接口提交VR成绩数据
    /// 根据超星API文档重新实现，确保完全符合接口要求
    /// </summary>
    public class ChaoXingScoreSubmitter : MonoBehaviour
    {
        [Header("配置")]
        [SerializeField] private ChaoXingConfig config;

        /// <summary>
        /// 提交成绩事件 (成功状态, 消息)
        /// </summary>
        public static event System.Action<bool, string> OnScoreSubmitted;

        #region 公共方法
        /// <summary>
        /// 提交成绩 - 根据API文档要求，score应为字符串类型
        /// </summary>
        /// <param name="score">分数（字符串类型）</param>
        /// <param name="experimentRecords">实验记录数组（可选）</param>
        public void SubmitScore(string score, object experimentRecords = null)
        {
            if (!ValidateSubmission(score)) return;

            var loginData = UserDataManager_ChaoXing.GetLoginData();
            if (loginData == null)
            {
                Debug.LogError("[ChaoXingScoreSubmitter] 未找到登录数据");
                OnScoreSubmitted?.Invoke(false, "未找到登录数据");
                return;
            }

            StartCoroutine(SubmitScoreCoroutine(score, loginData, experimentRecords));
        }

        /// <summary>
        /// 提交成绩 - 兼容数字参数的重载方法
        /// </summary>
        /// <param name="score">分数（数字格式）</param>
        /// <param name="experimentRecords">实验记录数组（可选）</param>
        public void SubmitScore(float score, object experimentRecords = null)
        {
            // 将数字转换为字符串格式
            string scoreString = score.ToString("F2");
            SubmitScore(scoreString, experimentRecords);
        }

        /// <summary>
        /// 设置配置
        /// </summary>
        public void SetConfig(ChaoXingConfig newConfig)
        {
            config = newConfig;
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 验证提交参数
        /// </summary>
        private bool ValidateSubmission(string score)
        {
            if (config == null || !config.IsValid())
            {
                Debug.LogError("[ChaoXingScoreSubmitter] 配置无效");
                OnScoreSubmitted?.Invoke(false, "配置无效");
                return false;
            }

            if (!config.EnableSubmission)
            {
                Debug.Log("[ChaoXingScoreSubmitter] 成绩提交功能已禁用");
                OnScoreSubmitted?.Invoke(false, "成绩提交功能已禁用");
                return false;
            }

            if (string.IsNullOrEmpty(score))
            {
                Debug.LogError("[ChaoXingScoreSubmitter] 分数不能为空");
                OnScoreSubmitted?.Invoke(false, "分数不能为空");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 提交成绩协程 - 支持重试机制
        /// </summary>
        private IEnumerator SubmitScoreCoroutine(string score, ChaoXingLoginData loginData, object experimentRecords)
        {
            int attempts = 0;
            int maxAttempts = config.RetryCount + 1; // 包括初始尝试

            while (attempts < maxAttempts)
            {
                attempts++;

                if (config.EnableDebugLog && attempts > 1)
                {
                    Debug.Log($"[ChaoXingScoreSubmitter] 第 {attempts} 次尝试提交成绩");
                }

                yield return StartCoroutine(PerformSingleSubmission(score, loginData, experimentRecords, attempts == maxAttempts));

                // 如果成功或者是最后一次尝试，则退出循环
                if (attempts >= maxAttempts)
                    break;

                // 等待重试间隔
                yield return new WaitForSeconds(config.RetryInterval);
            }
        }

        /// <summary>
        /// 执行单次提交
        /// </summary>
        private IEnumerator PerformSingleSubmission(string score, ChaoXingLoginData loginData, object experimentRecords, bool isLastAttempt)
        {
            var requestData = CreateRequestData(score, loginData, experimentRecords);
            var jsonData = JsonConvert.SerializeObject(requestData, Formatting.None);

            using (var request = new UnityWebRequest(config.ApiUrl, "POST"))
            {
                byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);
                request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/json");
                request.timeout = config.RequestTimeout;

                if (config.EnableDebugLog)
                {
                    Debug.Log($"[ChaoXingScoreSubmitter] 提交数据: {jsonData}");
                }

                yield return request.SendWebRequest();

                bool success = HandleResponse(request, isLastAttempt);
                if (success)
                {
                    yield break; // 成功则退出
                }
            }
        }

        /// <summary>
        /// 创建请求数据 - 根据API文档格式
        /// </summary>
        private object CreateRequestData(string score, ChaoXingLoginData loginData, object experimentRecords)
        {
            string handshake = GenerateHandshake(loginData);

            if (config.EnableDebugLog)
            {
                Debug.Log($"[ChaoXingScoreSubmitter] 登录数据详情:");
                Debug.Log($"  fid: '{loginData.fid}'");
                Debug.Log($"  resourceId: '{loginData.resourceId}'");
                Debug.Log($"  confSubjectId: '{loginData.confSubjectId}'");
                Debug.Log($"  classId: '{loginData.classId}'");
                Debug.Log($"  userId: '{loginData.userId}'");
                Debug.Log($"  score: '{score}'");
                Debug.Log($"  handshake: '{handshake}'");
                if (experimentRecords != null)
                {
                    Debug.Log($"  experimentRecords: {JsonConvert.SerializeObject(experimentRecords)}");
                }
            }

            // 根据API文档，构建请求数据
            var requestData = new
            {
                handshake = handshake,
                fid = loginData.fid,
                resourceId = loginData.resourceId,
                confSubjectId = loginData.confSubjectId,
                classId = loginData.classId,
                userId = loginData.userId,
                score = score // 字符串类型，符合API文档
            };

            // 如果有实验记录，添加到请求中
            if (experimentRecords != null)
            {
                return new
                {
                    handshake = handshake,
                    fid = loginData.fid,
                    resourceId = loginData.resourceId,
                    confSubjectId = loginData.confSubjectId,
                    classId = loginData.classId,
                    userId = loginData.userId,
                    score = score,
                    experimentRecords = experimentRecords
                };
            }

            return requestData;
        }

        /// <summary>
        /// 生成handshake验证参数 - 根据API文档要求
        /// md5(secret_key+fid+resourceId+userid+confSubjectId+date('yyyyMMdd')+classId)
        /// </summary>
        private string GenerateHandshake(ChaoXingLoginData loginData)
        {
            // 根据API文档，日期格式为yyyyMMdd，不包含时分秒
            string dateString = DateTime.Now.ToString("yyyyMMdd");

            // 按照API文档顺序拼接字符串
            string rawString = $"{config.SecretKey}{loginData.fid}{loginData.resourceId}{loginData.userId}{loginData.confSubjectId}{dateString}{loginData.classId}";

            if (config.EnableHandshakeDebug)
            {
                Debug.Log($"[ChaoXingScoreSubmitter] Handshake组成部分:");
                Debug.Log($"  secret_key: '{config.SecretKey}'");
                Debug.Log($"  fid: '{loginData.fid}'");
                Debug.Log($"  resourceId: '{loginData.resourceId}'");
                Debug.Log($"  userid: '{loginData.userId}'");
                Debug.Log($"  confSubjectId: '{loginData.confSubjectId}'");
                Debug.Log($"  date: '{dateString}'");
                Debug.Log($"  classId: '{loginData.classId}'");
                Debug.Log($"  原始字符串: '{rawString}'");
            }

            using (var md5 = MD5.Create())
            {
                byte[] hash = md5.ComputeHash(Encoding.UTF8.GetBytes(rawString));
                string result = BitConverter.ToString(hash).Replace("-", "").ToLower();

                if (config.EnableDebugLog)
                {
                    Debug.Log($"[ChaoXingScoreSubmitter] 生成的handshake: {result}");
                }

                return result;
            }
        }

        /// <summary>
        /// 处理响应 - 支持重试机制
        /// </summary>
        /// <param name="request">网络请求</param>
        /// <param name="isLastAttempt">是否为最后一次尝试</param>
        /// <returns>是否成功</returns>
        private bool HandleResponse(UnityWebRequest request, bool isLastAttempt)
        {
            if (request.result == UnityWebRequest.Result.Success)
            {
                if (config.EnableDebugLog)
                {
                    Debug.Log($"[ChaoXingScoreSubmitter] 响应: {request.downloadHandler.text}");
                }

                try
                {
                    var response = JsonConvert.DeserializeObject<dynamic>(request.downloadHandler.text);
                    string status = response?.status?.ToString();
                    string message = response?.msg?.ToString() ?? "未知响应";

                    bool success = status == "success";

                    if (success)
                    {
                        Debug.Log($"[ChaoXingScoreSubmitter] 成绩提交成功: {message}");
                        OnScoreSubmitted?.Invoke(true, message);
                        return true;
                    }
                    else
                    {
                        Debug.LogError($"[ChaoXingScoreSubmitter] 成绩提交失败: {message}");
                        if (isLastAttempt)
                        {
                            OnScoreSubmitted?.Invoke(false, message);
                        }
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[ChaoXingScoreSubmitter] 解析响应失败: {ex.Message}");
                    if (isLastAttempt)
                    {
                        OnScoreSubmitted?.Invoke(false, "响应解析失败");
                    }
                    return false;
                }
            }
            else
            {
                string errorMessage = $"网络错误: {request.error}";
                Debug.LogError($"[ChaoXingScoreSubmitter] {errorMessage}");

                if (isLastAttempt)
                {
                    OnScoreSubmitted?.Invoke(false, errorMessage);
                }
                return false;
            }
        }
        #endregion
    }
}