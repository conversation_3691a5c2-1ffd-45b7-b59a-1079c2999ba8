/* ===========
 * 作者: ZhuoRong
 * 创建时间: 2025/05/12
 * 非作者最新修改时间：  
 * =========== */
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.SceneManagement;
using Unity.Passport.Runtime;
using Unity.Passport.Runtime.UI;
using Nova;
using NaughtyAttributes;
using VIC.UI.WindowComponent;
using NovaSamples.UIControls;
using Passport;
using UnityEngine.Events;
using Unity.UOS.Auth;
using VIC.Utils;
using VIC.Networking.UOS;

namespace VIC.Networking.UOS.UI
{
    /// <summary>
    /// UOS 登录系统 UI 控制器 (NOVA 版本)
    /// 负责登录、角色创建及管理
    /// </summary>
    public class DemoUIControllerNOVA : MonoBehaviour
    {
        #region Singleton
        private static DemoUIControllerNOVA _instance;
        private static readonly object _instanceLock = new object();
        public static DemoUIControllerNOVA Instance => _instance;
        #endregion

        #region Inspector

        [Header("面板")]
        [SerializeField, Label("登录前")] private GameObject preLoginPanel;
        [SerializeField, Label("登录后")] private GameObject loggedInPanel;
        [SerializeField, Label("账号信息")] private GameObject userMessagePanel;
        // [SerializeField, Label("创建角色")] private GameObject createPersonaPanel;

        [Header("Passport UI")] // 添加 Passport UI 控制器字段
        [SerializeField] private PassportUIControllerNova_VICLogin passportUIController;

        [Header("用户信息")]
        [SerializeField, Label("账号 ID")] private TextBlock userIdNOVA;
        [SerializeField, Label("头像")]
        private UIBlock2D userAvatarNOVA;
        [SerializeField, Label("默认头像")]
        private Texture2D defaultAvatar;

        [Header("服务器/域")]
        [SerializeField, Label("当前服务器/域")]
        private TextBlock currentRealmText;
        [Header("服务器选择面板")]
        public GameObject realmListContent;
        public GameObject realmSelectPanel;
        public GameObject realmPrefab;
        public static readonly UnityEvent<Realm> SelectRealm = new();

        [Header("角色创建")]

        [SerializeField, Label("名称输入框")]
        private TextField createPersonaInputTextField;
        [SerializeField, Label("名字前缀列表 (逗号分隔)")]
        private string[] autoPrefixArray = new string[]
{
    "学徒工", "初级工","中级工", "高级工", "技师","高级技师","特级技师","首席技师"
};
        [SerializeField, Label("名字后缀列表 (逗号分隔)")]
        private string[] autoSuffixArray = new string[]
{
    "用户名称"
};

        #endregion

        #region Fields
        private string _currentRealm;
        private bool _isGettingPersona = false;
        private int _reAuthAttempts = 0;
        private const int MAX_REAUTH_ATTEMPTS = 2;
        private readonly PassportUIConfig_NOVA _config = new PassportUIConfig_NOVA
        {
            AutoRotation = true,
            InvokeLoginManually = false,
            UnityContainerId = "unity-container"
        };
        #endregion

        #region Unity Lifecycle
        private async void Start()
        {
            // 线程安全的单例设置
            lock (_instanceLock)
            {
                if (_instance != null && _instance != this)
                {
                    Debug.LogWarning("DemoUIControllerNOVA实例已存在，销毁重复实例");
                    Destroy(gameObject);
                    return;
                }
                _instance = this;
            }

            CloseAllPanels();
            Application.runInBackground = true;

            await InitializePassportAsync();
            preLoginPanel.SetActive(true);
            SelectRealm.AddListener(OnSelectRealm);

            Login();
        }
        #endregion

        #region Initialization
        private async Task InitializePassportAsync()
        {
#if UNITY_WEIXINMINIGAME
            await ExternalLoginAsync();
#else
            try
            {
                // 检查 passportUIController 是否已赋值
                if (passportUIController == null)
                {
                    Debug.LogError("Passport UI Controller 未在 Inspector 中赋值！");
                    return;
                }

                // 先尝试进行外部登录（如果有存储的登录信息）
                // await TryExternalLoginOnInit();

                // 将场景中已有的 passportUIController 实例传入 Init 方法
                await PassportUI_NOVA.Init(_config, OnPassportEvent, passportUIController);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Passport 初始化失败: {ex.Message}");
            }
#endif
        }


        #endregion

        #region Passport Callback
        private async void OnPassportEvent(PassportEvent_NOVA e)
        {
            switch (e)
            {
                case PassportEvent_NOVA.RejectedTos:
                    Debug.Log("用户拒绝协议");
                    ShowLoggedPanel(false);
                    break;

                case PassportEvent_NOVA.LoggedIn:
                    Debug.Log("完成登录");
                    break;

                case PassportEvent_NOVA.Completed:
                    Debug.Log("流程完成");

                    await HandlePostLoginAsync();
                    break;

                case PassportEvent_NOVA.Failed:
                    Debug.Log("登录失败");
                    HandleLoginFailure();
                    break;

                case PassportEvent_NOVA.LoggedOut:
                    Debug.Log("用户登出");
                    HandleLogout();
                    break;
            }
        }
        #endregion

        #region Login Failure Handling
        private void HandleLoginFailure()
        {
            Debug.Log("处理登录失败");

            // 确保显示登录界面
            ShowLoggedPanel(false);

            // 使用统一的缓存清理
            UOSExceptionHandler.ClearCaches("登录失败处理", true);
        }
        #endregion

        #region Post-Login Flow
        private async Task HandlePostLoginAsync()
        {
            // 使用统一的缓存清理，确保获取最新数据
            // VIC.Utils.CacheManager.ClearAllCaches("登录后流程");

            await FetchRealmAsync();

            // 确保头像预加载完成，避免用户点击更换头像时的延迟
            try
            {
                _ = CloudSaveManagement_UOS.EnsureAvatarsPreloaded(); // 异步执行，不阻塞UI
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"头像预加载启动失败，但不影响登录流程: {ex.Message}");
            }

            ShowLoggedPanel(true);
            UpdateUIAfterLogin();
        }

        private async Task FetchRealmAsync()
        {
            var realms = await UserDataManager_UOS.GetRealmList();
            if (realms.Count == 0)
            {
                UIMessage.Show("请在官网配置域 (Passport -> 域管理)", MessageType.Error);
                return;
            }
            GetRealm(realms);
        }
        #endregion

        #region UI Control
        private void ShowLoggedPanel(bool isLoggedIn)
        {
            preLoginPanel.SetActive(!isLoggedIn);

            loggedInPanel.SetActive(isLoggedIn);
            userMessagePanel.SetActive(isLoggedIn);
        }
        public void ShowRealmSelectPanel(bool show = true)
        {
            realmSelectPanel.SetActive(show);
        }
        public void BackToLoggedInPanel()
        {
            loggedInPanel.SetActive(true);
            userMessagePanel.SetActive(true);
            // createPersonaPanel.SetActive(false);
            realmSelectPanel.SetActive(false);
        }

        private void CloseAllPanels()
        {
            preLoginPanel.SetActive(false);
            loggedInPanel.SetActive(false);
            userMessagePanel.SetActive(false);
            // createPersonaPanel.SetActive(false);
        }
        #endregion

        #region Authentication
        public void Login()
        {
            ShowLoggedPanel(false);
            PassportUI_NOVA.Login();
        }

        public async void Logout()
        {
            // 使用统一的缓存清理
            VIC.Utils.CacheManager.ClearLoginCaches("用户登出");

            // 执行真实的VICLogin登出操作
            await VIC.Networking.VICLogin.UserDataManager_VICLogin.LogoutAsync();

            PassportUI_NOVA.Logout();
        }

        private void HandleLogout()
        {
            ShowLoggedPanel(false);
            userIdNOVA.Text = string.Empty;
            UserDataManager_UOS.HandleLogout();
        }
        #endregion

        #region Realm & Persona

        public async void StartGame()
        {
            if (!await TryExternalLogin())
                return;

            var persona = await UserDataManager_UOS.GetPersonaByRealm(_currentRealm);
            if (persona != null)
            {
                await OnSelectPersonaAsync(persona);
                EnterHall();
            }
            // else
            // {
            //     ShowCreatePersonaPanel();
            // }
        }

        // private void ShowCreatePersonaPanel()
        // {
        //     SetRandomName();
        //     userMessagePanel.SetActive(false);
        //     // createPersonaPanel.SetActive(true);
        // }

        public async void CreatePersona()
        {
            var name = createPersonaInputTextField.Text;
            if (string.IsNullOrWhiteSpace(name))
            {
                UIMessage.Show("请输入角色昵称", MessageType.Error);
                return;
            }

            if (!await TryExternalLogin(name))
                return;

            var persona = await UserDataManager_UOS.CreatePersona(name, _currentRealm);
            if (persona != null)
            {
                await OnSelectPersonaAsync(persona);
                EnterHall();
            }
        }

        /// <summary>
        /// 获取该应用下的域
        /// </summary>
        private async void GetRealm(List<Realm> list)
        {
            if (list == null) { return; }
            DestroyAllChildren(realmListContent.transform);
            try
            {

                foreach (var item in list)
                {
                    GameObject obj = Instantiate(realmPrefab, realmListContent.transform);
                    obj.GetComponent<RealmItemNOVA>().Set(item);
                }
                // 默认选择第一个域并获取角色信息
                await OnSelectRealmAsync(list[0]);
            }
            catch (PassportException e)
            {
                Debug.Log(e.Code);
            }
        }

        /// <summary>
        /// 选择服务器/域（异步版本）
        /// </summary>
        /// <param name="realm"></param>
        private async Task OnSelectRealmAsync(Realm realm)
        {
            Debug.Log($"用户选择域: {realm.Name} (ID: {realm.RealmID})");

            _currentRealm = realm.RealmID;
            currentRealmText.Text = realm.Name;

            UserDataManager_UOS.SetRealm(realm);

            // 选择域时只更新域信息，不进行完整登录
            Debug.Log($"域选择完成，当前域: {realm.RealmID}");

            // 获取该域下的角色信息并更新头像
            await GetPersonaByRealmAndUpdateAvatarAsync(realm.RealmID);

            BackToLoggedInPanel();
        }

        /// <summary>
        /// 选择服务器/域（同步版本，用于UI事件）
        /// </summary>
        /// <param name="realm"></param>
        private async void OnSelectRealm(Realm realm)
        {
            await OnSelectRealmAsync(realm);
        }

        /// <summary>
        /// 根据域ID获取角色信息并更新头像
        /// </summary>
        /// <param name="realmID">域ID</param>
        private async Task GetPersonaByRealmAndUpdateAvatarAsync(string realmID)
        {
            // 防止重复调用
            if (_isGettingPersona)
            {
                Debug.Log($"正在获取角色信息，跳过重复调用 - 域ID: {realmID}");
                return;
            }

            _isGettingPersona = true;

            try
            {
                Debug.Log($"开始获取域 {realmID} 下的角色信息...");

                // 检查UOS系统的登录状态
                if (!await UserDataManager_UOS.CheckUOSLoginStatusAsync())
                {
                    Debug.LogWarning("用户未登录UOS系统，无法获取角色信息。请点击开始游戏进行完整登录。");
                    userAvatarNOVA.SetImage(defaultAvatar);
                    return;
                }

                // 使用PassportSDK获取该域下的角色信息，添加30秒超时
                Debug.Log($"准备调用 PassportSDK.Identity.GetPersonaByRealm，域ID: {realmID}");
                var cts = new CancellationTokenSource();
                cts.CancelAfterSlim(TimeSpan.FromSeconds(30));

                Persona persona = null;
                try
                {
                    Debug.Log("开始调用 PassportSDK.Identity.GetPersonaByRealm...");
                    // 将Task转换为UniTask并添加超时
                    persona = await PassportSDK.Identity.GetPersonaByRealm(realmID).AsUniTask().AttachExternalCancellation(cts.Token);
                    Debug.Log($"PassportSDK.Identity.GetPersonaByRealm 调用完成，结果: {(persona != null ? "成功" : "null")}");
                }
                catch (OperationCanceledException)
                {
                    Debug.LogError($"获取域 {realmID} 下的角色信息超时（30秒）");
                    userAvatarNOVA.SetImage(defaultAvatar);
                    return;
                }

                if (persona != null)
                {
                    Debug.Log($"找到角色: {persona.DisplayName}");

                    // 同步角色信息到UserDataManager（这会异步获取头像URL）
                    Debug.Log("开始调用 UserDataManager_UOS.SelectPersona...");
                    await UserDataManager_UOS.SelectPersona(persona);
                    Debug.Log("UserDataManager_UOS.SelectPersona 调用完成");

                    // 确保SelectPersona完成后再更新头像
                    Debug.Log("开始调用 UpdatePersonaAvatarAsync...");
                    await UpdatePersonaAvatarAsync();
                    Debug.Log("UpdatePersonaAvatarAsync 调用完成");
                }
                else
                {
                    Debug.Log($"域 {realmID} 下没有找到角色");
                    // 没有角色时使用默认头像
                    userAvatarNOVA.SetImage(defaultAvatar);
                }
            }
            catch (Exception ex) when (ex.Message.Contains("未找到User") || ex.Message.Contains("Unauthenticated"))
            {
                Debug.LogWarning($"用户认证状态异常，尝试重新认证: {ex.Message}");

                // 检查重新认证尝试次数，防止无限递归
                if (_reAuthAttempts < MAX_REAUTH_ATTEMPTS)
                {
                    _reAuthAttempts++;
                    Debug.Log($"第 {_reAuthAttempts} 次重新认证尝试");

                    // 尝试重新进行外部登录
                    if (await TryExternalLogin())
                    {
                        Debug.Log("重新认证成功，重试获取角色信息");
                        // 重置标志并重试，但不递归调用
                        _isGettingPersona = false;
                        await GetPersonaByRealmAndUpdateAvatarAsync(realmID);
                        return; // 成功后直接返回
                    }
                    else
                    {
                        Debug.LogError($"第 {_reAuthAttempts} 次重新认证失败");
                    }
                }
                else
                {
                    Debug.LogError($"已达到最大重新认证次数 ({MAX_REAUTH_ATTEMPTS})，停止尝试");
                }

                // 认证失败或达到最大尝试次数，使用默认头像
                userAvatarNOVA.SetImage(defaultAvatar);
            }
            catch (Exception ex)
            {
                Debug.LogError($"获取域 {realmID} 下的角色信息失败: {ex.Message}");
                // 出错时使用默认头像
                userAvatarNOVA.SetImage(defaultAvatar);
            }
            finally
            {
                _isGettingPersona = false;
                // 成功完成或出现非认证异常时重置重新认证计数器
                _reAuthAttempts = 0;
            }
        }

        /// <summary>
        /// 更新角色头像
        /// </summary>
        private async Task UpdatePersonaAvatarAsync()
        {
            try
            {
                // 获取当前用户数据中的头像URL
                var userData = UserDataManager_UOS.CurrentUserData;

                Texture2D avatarTexture = null;

                if (!string.IsNullOrEmpty(userData.AvatarUrl))
                {
                    Debug.Log($"开始下载角色头像: {userData.AvatarUrl}");

                    // 为头像下载添加30秒超时
                    var cts = new CancellationTokenSource();
                    cts.CancelAfterSlim(TimeSpan.FromSeconds(30));

                    try
                    {
                        avatarTexture = await CloudSaveManagement_UOS.DownloadAvatarTextureAsync(userData.AvatarUrl).AttachExternalCancellation(cts.Token);
                    }
                    catch (OperationCanceledException)
                    {
                        Debug.LogError("角色头像下载超时（30秒），使用默认头像");
                        avatarTexture = null;
                    }

                    if (avatarTexture != null)
                    {
                        Debug.Log("角色头像下载成功");
                    }
                    else
                    {
                        Debug.LogWarning("角色头像下载失败，使用默认头像");
                    }
                }
                else
                {
                    Debug.Log("角色没有设置头像，使用默认头像");
                }

                // 更新UI头像
                userAvatarNOVA.SetImage(avatarTexture ?? defaultAvatar);
            }
            catch (Exception ex) when (ex.Message.Contains("未找到User") || ex.Message.Contains("Unauthenticated"))
            {
                Debug.LogWarning($"头像下载时用户认证异常，使用默认头像: {ex.Message}");
                userAvatarNOVA.SetImage(defaultAvatar);
            }
            catch (Exception ex)
            {
                Debug.LogError($"更新角色头像失败: {ex.Message}");
                userAvatarNOVA.SetImage(defaultAvatar);
            }
        }

        private async Task OnSelectPersonaAsync(Persona persona)
        {
            await UserDataManager_UOS.SelectPersona(persona);
        }

        private void EnterHall()
        {
            loggedInPanel.SetActive(false);
            userMessagePanel.SetActive(true);
            // createPersonaPanel.SetActive(false);

            UserDataManager_UOS.HandleLogIn().ContinueWith(_ =>
            {
                if (LoginWindowController.Instance != null)
                    LoginWindowController.Instance.NavigateToMainScene();
                else
                    SceneManager.LoadScene("UOSMainLauncher");
            }, TaskScheduler.FromCurrentSynchronizationContext());
        }

        private string GetRandomName()
        {
            string prefix = autoPrefixArray[UnityEngine.Random.Range(0, autoPrefixArray.Length)];
            string suffix = autoSuffixArray[UnityEngine.Random.Range(0, autoSuffixArray.Length)];

            // 不使用数字的命名模式
            string[] patterns = new[]
            {
                "{prefix}-{suffix}",    // 例如：引擎师-涡轮
                "{prefix}{suffix}",     // 例如：引擎师涡轮
                "{prefix}的{suffix}",   // 例如：引擎师的涡轮
                "{suffix}{prefix}"      // 例如：涡轮引擎师
            };

            string pattern = patterns[UnityEngine.Random.Range(0, patterns.Length)];
            return pattern.Replace("{prefix}", prefix).Replace("{suffix}", suffix);
        }

        public void SetRandomName()
        {
            createPersonaInputTextField.Text = GetRandomName();
        }
        #endregion

        #region UI Update
        private void UpdateUIAfterLogin()
        {
            var user = UserDataManager_UOS.CurrentUserData;
            userIdNOVA.Text = user.Name;

            // 登录后只更新用户名，头像更新由域选择时处理
            // 避免重复调用GetPersonaByRealmAndUpdateAvatarAsync
            Debug.Log("登录后UI更新完成，头像将在域选择时更新");
        }

        /// <summary>
        /// 删除 Transform 下的所有子对象
        /// </summary>
        /// <param name="parent"></param>
        private void DestroyAllChildren(Transform parent)
        {
            for (int i = 0; i < parent.childCount; i++)
            {
                Destroy(parent.GetChild(i).gameObject);
            }
        }
        #endregion

        #region Cancel/Return Button Logic

        /// <summary>
        /// 取消域选择，返回登录后主面板
        /// </summary>
        public void CancelRealmSelection()
        {
            BackToLoggedInPanel();
        }

        /// <summary>
        /// 取消角色创建，返回登录后主面板
        /// </summary>
        public void CancelPersonaCreation()
        {
            BackToLoggedInPanel();
        }

        /// <summary>
        /// 统一的返回按钮处理逻辑
        /// </summary>
        public void HandleBackButton()
        {
            // 根据当前显示的面板决定返回逻辑
            if (realmSelectPanel.activeInHierarchy)
            {
                CancelRealmSelection();
            }
            // else if (createPersonaPanel.activeInHierarchy)
            // {
            //     CancelPersonaCreation();
            // }
            else
            {
                // 默认返回登录前面板
                ShowLoggedPanel(false);
            }
        }
        #endregion

        #region External Login
        /// <summary>
        /// 尝试外部登录（在用户选择服务器后调用）
        /// 注意：此方法使用具体的realmID，因为用户已经选择了服务器
        /// 这个方法主要用于确保在特定域中有正确的角色绑定
        /// </summary>
        private async Task<bool> TryExternalLogin(string personaName = null)
        {
            // 如果没有选择域，不进行域特定的外部登录
            if (string.IsNullOrEmpty(_currentRealm))
            {
                Debug.LogWarning("[DemoUIControllerNOVA] 当前域为空，跳过域特定的外部登录");
                return true;
            }

            // 优先尝试使用超星外部登录信息
            if (!string.IsNullOrEmpty(VIC.Networking.ChaoXing.CustomProtocolHandler.StoredUserId))
            {
                try
                {
                    string userId = VIC.Networking.ChaoXing.CustomProtocolHandler.StoredUserId;
                    string personaId = VIC.Networking.ChaoXing.CustomProtocolHandler.StoredPersonaId;
                    string personaDisplayName = personaName ?? personaId; // 使用传入的personaName或默认使用personaId
                    string realmID = _currentRealm;

                    Debug.Log($"[DemoUIControllerNOVA] 使用超星外部登录信息进行域特定UOS登录 - UserId: {userId}, PersonaId: {personaId}, PersonaDisplayName: {personaDisplayName}, RealmID: {realmID}");

                    // 按照官方示例的参数顺序：userId, personaId, personaDisplayName, realmID
                    await AuthTokenManager.ExternalLogin(
                        userId,
                        personaId,
                        personaDisplayName,
                        realmID
                    );

                    Debug.Log("[DemoUIControllerNOVA] 超星域特定外部登录成功");

                    // 将UOS的UserID存储到超星用户数据管理器中
                    if (UserDataManager_UOS.CurrentUserData.UserID != null)
                    {
                        VIC.Networking.ChaoXing.UserDataManager_ChaoXing.StoredUOSId = UserDataManager_UOS.CurrentUserData.UserID;
                        Debug.Log($"[DemoUIControllerNOVA] 已将UOS UserID存储到超星管理器: {UserDataManager_UOS.CurrentUserData.UserID}");
                    }

                    return true;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[DemoUIControllerNOVA] 超星域特定外部登录失败: {ex.Message}");
                    UIMessage.Show($"外部登录失败: {ex.Message}", MessageType.Error);
                    return false;
                }
            }
            // 如果超星登录信息不存在，尝试使用VICLogin登录信息
            else if (VIC.Networking.VICLogin.VICLoginStorageHandler.HasStoredLoginInfo)
            {
                try
                {
                    // 验证VICLogin登录信息的完整性
                    if (!VIC.Networking.VICLogin.VICLoginStorageHandler.IsLoginInfoValid())
                    {
                        Debug.LogError("[DemoUIControllerNOVA] VICLogin登录信息不完整，无法进行域特定外部登录");
                        UIMessage.Show("登录信息不完整，请重新登录", MessageType.Error);
                        return false;
                    }

                    string userId = VIC.Networking.VICLogin.VICLoginStorageHandler.GetStoredUserId();
                    string personaId = VIC.Networking.VICLogin.VICLoginStorageHandler.GetStoredPersonaId();
                    string personaDisplayName = personaName ?? VIC.Networking.VICLogin.VICLoginStorageHandler.GetStoredPersonaName();
                    string realmID = _currentRealm;

                    Debug.Log($"[DemoUIControllerNOVA] 使用VICLogin外部登录信息进行域特定UOS登录 - UserId: {userId}, PersonaId: {personaId}, PersonaDisplayName: {personaDisplayName}, RealmID: {realmID}");

                    // 按照官方示例的参数顺序：userId, personaId, personaDisplayName, realmID
                    await AuthTokenManager.ExternalLogin(
                        userId,
                        personaId,
                        personaDisplayName,
                        realmID
                    );

                    Debug.Log("[DemoUIControllerNOVA] VICLogin域特定外部登录成功");
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[DemoUIControllerNOVA] VICLogin域特定外部登录失败: {ex.Message}");
                    UIMessage.Show($"外部登录失败: {ex.Message}", MessageType.Error);
                    return false;
                }
            }

            return true; // 如果没有外部登录信息，返回true继续正常流程
        }
        #endregion
    }
}
