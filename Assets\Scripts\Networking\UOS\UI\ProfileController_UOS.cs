/*===========
*作者: ZhuoRong
*创建时间: 2025/03/06
*非作者最新修改时间：2025/03/15
===========*/
using UnityEngine;
using Nova;
using NovaSamples.UIControls;
using NaughtyAttributes;
using System;
using VIC.Utils;
using VIC.UI;
using System.Collections;
using VIC.UI.Avatar;
using Unity.Passport.Runtime.UI;
using Unity.Passport.Runtime;
using System.Threading.Tasks; // 添加Task支持

namespace VIC.Networking.UOS.UI
{
    ///<summary>
    /// 负责个人信息（包括头像选择）显示与修改的逻辑
    ///</summary>
    public class ProfileController_UOS : MonoBehaviour
    {

        #region 排行榜相关
        [Header("排行榜")]
        [SerializeField, Label("排行榜管理器")]
        private ChartsManager chartsManager;
        #endregion

        [Header("总页面")]
        [SerializeField, Label("设置页面")]
        private GameObject gameSetPanel;

        // [SerializeField] private UIMessageController messageController;

        #region 主页组件
        [Header("主页")]
        [SerializeField, Label("用户名")]
        private TextBlock userName;
        [SerializeField, Label("用户头像")]
        private UIBlock2D userAvatar;


        [SerializeField, Label("拥有的金币")]
        private TextBlock GOLD_COINCount;
        [SerializeField, Label("充值页面拥有的金币")]
        private TextBlock GOLD_COINCountFromRecharge;
        #endregion

        #region 个人信息详情页面组件
        [Header("个人信息详情页面")]
        [SerializeField, Label("用户名输入框")]
        private TextField userNameField;
        [SerializeField, Label("账号输入框")]
        private TextField userIDField;
        [SerializeField, Label("角色姓名输入框")]
        private TextField personaNameField;
        [SerializeField, Label("所在服务器输入框")]
        private TextField realmIDField;
        [SerializeField, Label("排行榜信息输入框")]
        private TextField profileField;
        [SerializeField, Label("头像图片")]
        private UIBlock2D avatarImage;
        [SerializeField, Label("修改头像按钮")]
        private Button changeAvatarButton;
        [SerializeField, Label("删除头像按钮")]
        private Button clearAvatarButton;
        [SerializeField, Label("头像选择列表预制体")]
        private GameObject avatarSelectionPanelPrefab;
        [SerializeField, Label("头像选择按钮生成父节点")]
        private Transform panelParent;
        [SerializeField, Label("保存按钮")]
        private Button saveButton;
        [SerializeField, Label("取消按钮")]
        private UIButton cancelButton;
        [SerializeField, Label("加载指示器")]
        private UIBlock loadingIndicator;
        #endregion

        #region 私有字段
        private Texture2D currentAvatar;
        private Texture2D originalAvatar; // 添加原始头像引用
        private string currentAvatarId; // 当前选择的头像ID（如果是默认头像）
        private Texture2D defaultAvatar;
        private Coroutine avatarLoadingCoroutine;
        private bool isUpdatingProfile = false;
        private UserData_UOS originalUserData;
        #endregion

        #region 生命周期与事件注册
        private void Start()
        {
            if (loadingIndicator != null)
                loadingIndicator.gameObject.SetActive(false);

            // 保存原始用户数据，用于取消编辑时恢复
            originalUserData = UserDataManager_UOS.CurrentUserData;
            RegisterEvents();

            // 每次Start时重新获取当前账户的头像
            RefreshCurrentUserAvatar();

            UpdateUIAsync();

            UserDataManager_UOS.OnUserDataChanged += OnUserDataChanged;

            // 绑定金币事件
            TradingSystemManagement_UOS.EconomyEvents.OnGOLD_COINChanged += OnGOLD_COINUpdated;
            TradingSystemManagement_UOS.EconomyEvents.OnInventoryLoaded += OnInventoryLoaded;

            // 异步初始化金币显示，确保背包数据已加载
            _ = InitializeGoldDisplayAsync().ContinueWith(task =>
            {
                if (task.IsFaulted)
                {
                    UnityEngine.Debug.LogError($"初始化金币显示失败: {task.Exception?.GetBaseException().Message}");
                }
            }, TaskScheduler.FromCurrentSynchronizationContext());
        }

        /// <summary>
        /// 异步初始化金币显示
        /// </summary>
        private async Task InitializeGoldDisplayAsync()
        {
            try
            {
                // 确保背包数据已加载
                await TradingSystemManagement_UOS.GetPersonaInventory();

                // 获取金币数量并更新显示
                uint gold = TradingSystemManagement_UOS.GetItemQuantity("GOLD_COIN");
                // Debug.Log($"金币数量：{gold}");

                // 更新UI显示
                OnGOLD_COINUpdated(gold);
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"初始化金币显示失败: {ex.Message}");
                // 失败时显示0
                OnGOLD_COINUpdated(0);
            }
        }

        private void OnGOLD_COINUpdated(uint newAmount)
        {
            GOLD_COINCount.Text = newAmount.ToString();
            GOLD_COINCountFromRecharge.Text = newAmount.ToString();
        }

        /// <summary>
        /// 背包加载完成事件处理
        /// </summary>
        private void OnInventoryLoaded()
        {
            // 背包加载完成后，更新金币显示
            uint gold = TradingSystemManagement_UOS.GetItemQuantity("GOLD_COIN");
            // Debug.Log($"背包加载完成，金币数量：{gold}");
            OnGOLD_COINUpdated(gold);
        }

        private void OnDestroy()
        {
            UnregisterEvents();
            UserDataManager_UOS.OnUserDataChanged -= OnUserDataChanged;
            if (avatarLoadingCoroutine != null)
            {
                StopCoroutine(avatarLoadingCoroutine);
                avatarLoadingCoroutine = null;
            }

            TradingSystemManagement_UOS.EconomyEvents.OnGOLD_COINChanged -= OnGOLD_COINUpdated;
            TradingSystemManagement_UOS.EconomyEvents.OnInventoryLoaded -= OnInventoryLoaded;
        }

        private void RegisterEvents()
        {
            saveButton?.OnClicked.AddListener(OnSaveProfileButtonClicked);
            changeAvatarButton?.OnClicked.AddListener(ShowAvatarSelection);
            clearAvatarButton?.OnClicked.AddListener(ClearAvatar);
            cancelButton?.OnClicked.AddListener(OnCancelButtonClicked);
            // addGOLD_COINButton?.OnClicked.AddListener(OnAddButtonClickedAsync);
        }

        private void UnregisterEvents()
        {
            saveButton?.OnClicked.RemoveAllListeners();
            changeAvatarButton?.OnClicked.RemoveAllListeners();
            clearAvatarButton?.OnClicked.RemoveAllListeners();
            cancelButton?.OnClicked.RemoveAllListeners();
        }
        /// <summary>
        /// 刷新当前用户头像（用于账户切换时）
        /// </summary>
        private void RefreshCurrentUserAvatar()
        {
            // 清除之前的头像缓存
            currentAvatar = null;
            originalAvatar = null;
            currentAvatarId = null;

            // 如果有正在进行的头像加载协程，停止它
            if (avatarLoadingCoroutine != null)
            {
                StopCoroutine(avatarLoadingCoroutine);
                avatarLoadingCoroutine = null;
            }

            // 立即开始加载当前账户的头像
            avatarLoadingCoroutine = StartCoroutine(GetAvatarAndUpdateUI());
        }
        #endregion

        #region UI更新
        ///<summary>
        /// 更新主页与详情页面UI
        ///</summary>
        public async Task UpdateUIAsync()
        {
            if (userNameField != null)
                userNameField.Text = UserDataManager_UOS.CurrentUserData.Name ?? string.Empty;
            if (userIDField != null)
                userIDField.Text = UserDataManager_UOS.CurrentUserData.UserID ?? string.Empty;
            if (personaNameField != null)
                personaNameField.Text = UserDataManager_UOS.CurrentUserData.PersonaName ?? string.Empty;
            if (realmIDField != null)
                realmIDField.Text = UserDataManager_UOS.CurrentUserData.RealmName ?? string.Empty;

            // 更新排行榜信息
            await UpdateLeaderboardInfo();


            if (userName != null)
                userName.Text = UserDataManager_UOS.CurrentUserData.Name ?? string.Empty;

            // 只有在没有正在进行的头像加载时才启动新的加载
            if (avatarLoadingCoroutine == null)
            {
                avatarLoadingCoroutine = StartCoroutine(GetAvatarAndUpdateUI());
            }

            originalUserData = UserDataManager_UOS.CurrentUserData;
        }

        /// <summary>
        /// 更新排行榜信息显示
        /// </summary>
        private async Task UpdateLeaderboardInfo()
        {
            if (profileField == null)
                return;

            try
            {
                // 如果没有设置ChartsManager，尝试自动查找
                if (chartsManager == null)
                {
                    chartsManager = FindObjectOfType<ChartsManager>();
                }

                if (chartsManager != null)
                {
                    // 获取排行榜数据
                    var leaderboardData = await chartsManager.GetCurrentUserScoreAndTier();

                    if (leaderboardData != null)
                    {
                        // 格式化排行榜信息显示
                        string leaderboardInfo = $"排名: {leaderboardData.Rank} | 分数: {leaderboardData.Score:F0} | 等级: {leaderboardData.Tier}";
                        profileField.Text = leaderboardInfo;
                    }
                    else
                    {
                        profileField.Text = "暂无排行榜数据";
                    }
                }
                else
                {
                    profileField.Text = "排行榜管理器未找到";
                }
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"更新排行榜信息失败: {ex.Message}");
                profileField.Text = "排行榜信息获取失败";
            }
        }

        /// <summary>
        /// 手动刷新排行榜信息（可供外部调用）
        /// </summary>
        public async void RefreshLeaderboardInfo()
        {
            await UpdateLeaderboardInfo();
        }

        ///<summary>
        /// 异步获取头像并更新UI
        ///</summary>
        private IEnumerator GetAvatarAndUpdateUI()
        {
            if (loadingIndicator != null)
                loadingIndicator.gameObject.SetActive(true);

            // 使用CloudSaveManagement_UOS下载头像
            yield return CloudSaveManagement_UOS.DownloadAvatarSpriteCoroutine(
                UserDataManager_UOS.CurrentUserData.AvatarUrl,
                (sprite) =>
                {
                    if (sprite != null)
                    {
                        currentAvatar = sprite.texture;
                        originalAvatar = sprite.texture; // 保存原始头像
                        UpdateAvatarUI(currentAvatar);
                    }
                    else
                    {
                        // Debug.LogError("获取头像失败，使用默认头像");
                        currentAvatar = GetDefaultAvatar();
                        originalAvatar = GetDefaultAvatar(); // 保存原始头像
                        UpdateAvatarUI(currentAvatar);
                    }
                });

            if (loadingIndicator != null)
                loadingIndicator.gameObject.SetActive(false);

            // 清除协程引用
            avatarLoadingCoroutine = null;
        }

        ///<summary>
        /// 获取默认头像
        ///</summary>
        private Texture2D GetDefaultAvatar()
        {
            // 创建一个简单的默认头像纹理
            if (defaultAvatar == null)
            {
                defaultAvatar = new Texture2D(1, 1);
                defaultAvatar.SetPixel(0, 0, Color.gray);
                defaultAvatar.Apply();
            }
            return defaultAvatar;
        }

        ///<summary>
        /// 判断传入头像是否为默认头像（基于引用比较默认头像缓存）
        ///</summary>
        private bool IsDefaultAvatar(Texture2D avatar)
        {
            if (avatar == null)
                return true;
            Texture2D def = GetDefaultAvatar();
            return def != null && (avatar == def || avatar.name == def.name);
        }

        ///<summary>
        /// 判断头像是否已更改
        ///</summary>
        private bool IsAvatarChanged()
        {
            // 如果当前头像为空，认为没有更改
            if (currentAvatar == null)
                return false;

            // 如果原始头像为空，但当前头像不为空且不是默认头像，认为已更改
            if (originalAvatar == null)
                return !IsDefaultAvatar(currentAvatar);

            // 比较当前头像与原始头像是否不同
            // 使用纹理内容比较而不是引用比较，因为从云端下载的纹理可能是新实例
            return !AreTexturesEqual(currentAvatar, originalAvatar);
        }

        ///<summary>
        /// 比较两个纹理是否相等（基于内容比较）
        ///</summary>
        private bool AreTexturesEqual(Texture2D tex1, Texture2D tex2)
        {
            if (tex1 == null && tex2 == null)
                return true;
            if (tex1 == null || tex2 == null)
                return false;
            if (tex1 == tex2)
                return true;

            // 比较纹理尺寸
            if (tex1.width != tex2.width || tex1.height != tex2.height)
                return false;

            // 如果纹理名称相同，认为是同一个纹理
            if (!string.IsNullOrEmpty(tex1.name) && !string.IsNullOrEmpty(tex2.name) && tex1.name == tex2.name)
                return true;

            // 对于小纹理，可以进行像素级比较
            if (tex1.width * tex1.height <= 1024) // 32x32 或更小
            {
                try
                {
                    var pixels1 = tex1.GetPixels();
                    var pixels2 = tex2.GetPixels();
                    if (pixels1.Length != pixels2.Length)
                        return false;

                    for (int i = 0; i < pixels1.Length; i++)
                    {
                        if (pixels1[i] != pixels2[i])
                            return false;
                    }
                    return true;
                }
                catch
                {
                    // 如果无法读取像素，回退到引用比较
                    return false;
                }
            }

            // 对于大纹理，只能进行引用比较
            return false;
        }

        ///<summary>
        /// 更新头像显示控件
        ///</summary>
        private void UpdateAvatarUI(Texture2D avatar)
        {
            if (avatar == null)
                avatar = GetDefaultAvatar();
            if (userAvatar != null)
                userAvatar.SetImage(avatar);
            if (avatarImage != null)
                avatarImage.SetImage(avatar);
        }

        #endregion

        #region 事件处理
        ///<summary>
        /// 用户数据变更事件处理
        ///</summary>
        private void OnUserDataChanged(UserData_UOS userData)
        {
            UpdateUIAsync();
        }

        #endregion

        #region 按钮事件处理
        /// <summary>
        /// 显示头像选择面板
        /// </summary>
        private void ShowAvatarSelection()
        {
            if (avatarSelectionPanelPrefab == null || isUpdatingProfile)
                return;
            string panelId = $"AvatarSelectionPanel_{avatarSelectionPanelPrefab.name}";
            GameObject panel = ObjectPool.Instance.GetOrCreateObject(panelId, avatarSelectionPanelPrefab, panelParent);
            if (panel.TryGetComponent<AvatarSelectionPanel>(out var selectionPanel))
            {
                selectionPanel.OnAvatarSelected -= OnAvatarSelected;
                selectionPanel.OnAvatarSelected += OnAvatarSelected;
            }
        }

        /// <summary>
        /// 处理头像选择事件
        /// </summary>
        /// <param name="selectedAvatar">选择的头像</param>
        /// <param name="avatarId">头像ID（如果是默认头像）</param>
        private void OnAvatarSelected(Texture2D selectedAvatar, string avatarId = null)
        {
            if (selectedAvatar != null)
            {
                currentAvatar = selectedAvatar;
                currentAvatarId = avatarId; // 保存头像ID
                UpdateAvatarUI(currentAvatar);
            }
        }

        /// <summary>
        /// 清除头像
        /// </summary>
        private void ClearAvatar()
        {
            currentAvatar = GetDefaultAvatar();
            currentAvatarId = null; // 清除头像ID
            UpdateAvatarUI(currentAvatar);
        }
        /// <summary>
        /// 取消编辑按钮点击事件
        /// </summary>
        private void OnCancelButtonClicked()
        {
            // 恢复原始数据
            UserDataManager_UOS.UpdateUserData(originalUserData);

            // 恢复原始头像
            if (originalAvatar != null)
            {
                currentAvatar = originalAvatar;
                UpdateAvatarUI(currentAvatar);
            }

            // 更新UI
            UpdateUIAsync();

            // 显示取消消息
            // Debug.Log("已取消编辑");

        }
        /// <summary>
        /// 保存个人资料按钮点击事件
        /// </summary>
        private async Task OnSaveProfileButtonClickedAsync()
        {
            if (isUpdatingProfile)
                return;

            isUpdatingProfile = true;
            if (loadingIndicator != null)
                loadingIndicator.gameObject.SetActive(true);

            try
            {
                string newUserName = userNameField != null ? userNameField.Text : string.Empty;
                bool UserNameChanged = !string.IsNullOrEmpty(newUserName) && newUserName != originalUserData.Name;

                string newPersonName = personaNameField != null ? personaNameField.Text : string.Empty;
                bool PersonChanged = !string.IsNullOrEmpty(newPersonName) && newPersonName != originalUserData.PersonaName;

                bool avatarChanged = IsAvatarChanged();

                if (!UserNameChanged && !avatarChanged && !PersonChanged)
                {
                    UIMessage.Show("没有检测到修改内容", MessageType.Error);
                    isUpdatingProfile = false;
                    if (loadingIndicator != null)
                        loadingIndicator.gameObject.SetActive(false);
                    return;
                }

                // 头像处理
                string avatarUrl = null;
                if (avatarChanged)
                {
                    // 检查是否是默认头像
                    if (!string.IsNullOrEmpty(currentAvatarId))
                    {
                        // 使用默认头像ID设置用户头像
                        bool success = await CloudSaveManagement_UOS.SetUserProfilePictureByIdAsync(
                            currentAvatarId,
                            UserDataManager_UOS.CurrentUserData.PersonaID
                        );

                        if (success)
                        {
                            // Debug.Log($"成功设置默认头像，ID: {currentAvatarId}");
                            // 获取当前用户头像信息以更新本地数据
                            var currentUserPfp = await CloudSaveManagement_UOS.GetCurrentUserProfilePictureAsync();
                            avatarUrl = currentUserPfp?.DownloadUrl;
                        }
                        else
                        {
                            throw new Exception("设置默认头像失败");
                        }
                    }
                    else
                    {
                        // 使用自定义头像（Base64编码）
                        avatarUrl = "data:image/png;base64," + TextureToBase64(currentAvatar);
                        // 调用UOS API更新用户信息
                        await Unity.Passport.Runtime.PassportSDK.Identity.UpdateUserProfileInfo(
                            null, avatarUrl
                        );
                    }
                }

                // 名称处理
                string nameParam = UserNameChanged ? newUserName : null;
                if (UserNameChanged && !string.IsNullOrEmpty(UserDataManager_UOS.CurrentUserData.UserID))
                {
                    // 调用UOS API更新用户信息
                    await Unity.Passport.Runtime.PassportSDK.Identity.UpdateUserProfileInfo(
                        newUserName
                    );
                }

                string personNameParam = PersonChanged ? newPersonName : null;
                // 更新角色信息（如果有修改）
                if (PersonChanged && !string.IsNullOrEmpty(UserDataManager_UOS.CurrentUserData.PersonaID))
                {
                    await Unity.Passport.Runtime.PassportSDK.Identity.UpdatePersona(
                        personNameParam
                    );
                }

                // 更新本地用户数据
                var updatedUserData = UserDataManager_UOS.CurrentUserData;
                if (UserNameChanged)
                    updatedUserData.Name = newUserName;
                if (avatarChanged)
                    updatedUserData.AvatarUrl = avatarUrl;
                if (PersonChanged)
                    updatedUserData.PersonaName = personNameParam;

                UserDataManager_UOS.UpdateUserData(updatedUserData);

                UIMessage.Show("个人信息更新成功", MessageType.Info);
            }
            catch (Exception ex)
            {
                UIMessage.Show("保存个人信息失败:" + ex.Message, MessageType.Error);
            }
            finally
            {
                isUpdatingProfile = false;
                if (loadingIndicator != null)
                    loadingIndicator.gameObject.SetActive(false);
            }
        }

        /// <summary>
        /// 保存个人资料按钮点击事件包装方法
        /// </summary>
        private void OnSaveProfileButtonClicked()
        {
            _ = OnSaveProfileButtonClickedAsync().ContinueWith(task =>
            {
                if (task.IsFaulted)
                {
                    UnityEngine.Debug.LogError($"保存个人资料失败: {task.Exception?.GetBaseException().Message}");
                    UIMessage.Show($"保存个人资料失败: {task.Exception?.GetBaseException().Message}", MessageType.Error);

                    // 确保在异常情况下重置状态
                    isUpdatingProfile = false;
                    if (loadingIndicator != null)
                        loadingIndicator.gameObject.SetActive(false);
                }
            }, TaskScheduler.FromCurrentSynchronizationContext());
        }

        /// <summary>
        /// 将纹理转换为Base64字符串
        /// </summary>
        /// <param name="texture">纹理</param>
        /// <returns>Base64字符串</returns>
        private static string TextureToBase64(Texture2D texture)
        {
            if (texture == null) return string.Empty;
            byte[] bytes = texture.EncodeToPNG();
            return Convert.ToBase64String(bytes);
        }

        #endregion

    }
}
